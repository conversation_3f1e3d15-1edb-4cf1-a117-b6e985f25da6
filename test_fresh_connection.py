#!/usr/bin/env python3
"""
Fresh test of MySQL connection without caching
"""

import sys
import os

# Clear any cached modules
if 'config' in sys.modules:
    del sys.modules['config']

try:
    # Import fresh config
    from config import DATABASE_CONFIG
    import mysql.connector
    from mysql.connector import <PERSON>rror
    
    print("Testing MySQL connection with fresh config...")
    print(f"Host: {DATABASE_CONFIG['host']}")
    print(f"User: {DATABASE_CONFIG['user']}")
    print(f"Port: {DATABASE_CONFIG['port']}")
    print(f"Password length: {len(DATABASE_CONFIG['password'])}")
    print(f"Password starts with: {DATABASE_CONFIG['password'][:3]}..." if DATABASE_CONFIG['password'] else "No password")
    
    # Test connection without database
    config = DATABASE_CONFIG.copy()
    config.pop('database')  # Remove database name for initial connection
    
    print("\nAttempting connection...")
    connection = mysql.connector.connect(**config)
    print("✅ MySQL connection successful!")
    
    cursor = connection.cursor()
    cursor.execute("SELECT VERSION()")
    version = cursor.fetchone()
    print(f"✅ MySQL version: {version[0]}")
    
    # Test if we can create databases
    cursor.execute("SHOW DATABASES")
    databases = cursor.fetchall()
    print(f"✅ Can access databases. Found {len(databases)} databases.")
    
    cursor.close()
    connection.close()
    
    print("\n🎉 MySQL is ready! You can now run the setup script.")
    
except Error as e:
    print(f"❌ MySQL connection failed: {e}")
    print(f"Error code: {e.errno}")
    print(f"SQL state: {e.sqlstate}")
    
    if e.errno == 1045:
        print("\n🔧 Authentication failed. Possible solutions:")
        print("1. Double-check your password in config.py")
        print("2. Try connecting with MySQL Workbench or command line first")
        print("3. Make sure the MySQL user 'root' exists and has the correct password")
    elif e.errno == 2003:
        print("\n🔧 Cannot connect to MySQL server. Possible solutions:")
        print("1. Make sure MySQL server is running")
        print("2. Check if the port 3306 is correct")
        print("3. Try starting MySQL service")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    print(f"Error type: {type(e).__name__}")
