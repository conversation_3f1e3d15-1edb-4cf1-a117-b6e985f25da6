import tkinter as tk
from tkinter import messagebox
from library_system.gui import LibraryGUI
import sys

def main():
    """Main application entry point"""
    try:
        # Create main window
        root = tk.Tk()

        # Create and run the GUI application
        app = LibraryGUI(root)

        # Handle window close event
        def on_closing():
            if messagebox.askokcancel("Quit", "Do you want to quit?"):
                try:
                    app.library.db.disconnect()
                except:
                    pass
                root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)

        # Start the GUI event loop
        root.mainloop()

    except Exception as e:
        messagebox.showerror("Error", f"Failed to start application: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
