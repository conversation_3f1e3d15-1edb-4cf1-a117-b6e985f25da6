import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class ModernDialog:
    """Base class for modern dialogs"""
    def __init__(self, parent, title):
        self.result = None
        self.parent = parent
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.configure(bg='#F8F9FA')
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Colors
        self.colors = {
            'primary': '#2E86AB',
            'success': '#28A745',
            'danger': '#DC3545',
            'light': '#F8F9FA',
            'dark': '#343A40',
            'white': '#FFFFFF',
            'gray': '#6C757D'
        }
        
        # Center the dialog
        self.center_dialog()
    
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def create_header(self, title, icon="📝"):
        """Create modern header"""
        header_frame = tk.Frame(self.dialog, bg=self.colors['primary'], height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        header_label = tk.Label(header_frame, text=f"{icon} {title}",
                               font=('Segoe UI', 18, 'bold'),
                               fg=self.colors['white'], bg=self.colors['primary'])
        header_label.pack(expand=True)
    
    def create_form_field(self, parent, label_text, field_type="entry", required=False, **kwargs):
        """Create a form field with label"""
        field_frame = tk.Frame(parent, bg=self.colors['white'])
        field_frame.pack(fill=tk.X, pady=8)
        
        # Label
        req_text = " *" if required else ""
        label = tk.Label(field_frame, text=f"{label_text}{req_text}",
                        font=('Segoe UI', 11, 'bold' if required else 'normal'),
                        fg=self.colors['dark'], bg=self.colors['white'])
        label.pack(anchor=tk.W, pady=(0, 5))
        
        # Field
        if field_type == "entry":
            field = tk.Entry(field_frame, font=('Segoe UI', 11), **kwargs)
            field.pack(fill=tk.X, ipady=8)
        elif field_type == "text":
            field = tk.Text(field_frame, font=('Segoe UI', 11), height=3, **kwargs)
            field.pack(fill=tk.X)
        elif field_type == "spinbox":
            field = tk.Spinbox(field_frame, font=('Segoe UI', 11), **kwargs)
            field.pack(fill=tk.X, ipady=8)
        
        return field
    
    def create_buttons(self, parent, primary_text, primary_command):
        """Create dialog buttons"""
        button_frame = tk.Frame(parent, bg=self.colors['white'])
        button_frame.pack(fill=tk.X, pady=20)
        
        # Primary button
        primary_btn = tk.Button(button_frame, text=primary_text,
                               font=('Segoe UI', 11, 'bold'),
                               fg=self.colors['white'], bg=self.colors['primary'],
                               relief=tk.FLAT, cursor='hand2',
                               command=primary_command, padx=30, pady=10)
        primary_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Cancel button
        cancel_btn = tk.Button(button_frame, text="Cancel",
                              font=('Segoe UI', 11),
                              fg=self.colors['dark'], bg=self.colors['light'],
                              relief=tk.FLAT, cursor='hand2',
                              command=self.dialog.destroy, padx=30, pady=10)
        cancel_btn.pack(side=tk.RIGHT)


class ModernBookDialog(ModernDialog):
    """Modern dialog for adding books"""
    def __init__(self, parent, library, title):
        self.library = library
        super().__init__(parent, title)
        self.dialog.geometry("500x600")
        self.create_widgets()
    
    def create_widgets(self):
        """Create book dialog widgets"""
        # Header
        self.create_header("Add New Book", "📚")
        
        # Form container
        form_container = tk.Frame(self.dialog, bg=self.colors['white'])
        form_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)
        
        # Form fields
        self.title_entry = self.create_form_field(form_container, "Book Title", required=True)
        self.author_entry = self.create_form_field(form_container, "Author", required=True)
        self.isbn_entry = self.create_form_field(form_container, "ISBN", required=True)
        self.year_entry = self.create_form_field(form_container, "Published Year", "spinbox",
                                                 from_=1000, to=datetime.now().year, value=datetime.now().year)
        self.category_entry = self.create_form_field(form_container, "Category")
        
        # Set default category
        self.category_entry.insert(0, "General")
        
        # Buttons
        self.create_buttons(form_container, "📚 Add Book", self.add_book)
    
    def add_book(self):
        """Add the book"""
        try:
            title = self.title_entry.get().strip()
            author = self.author_entry.get().strip()
            isbn = self.isbn_entry.get().strip()
            year_str = self.year_entry.get().strip()
            category = self.category_entry.get().strip() or "General"
            
            # Validate required fields
            if not all([title, author, isbn]):
                messagebox.showerror("Validation Error", 
                                   "Please fill in all required fields (marked with *)")
                return
            
            # Convert year
            year = int(year_str) if year_str and year_str.isdigit() else None
            
            # Add book
            book_id = self.library.add_book(title, author, isbn, year, category)
            self.result = {'id': book_id, 'title': title}
            
            messagebox.showinfo("Success", f"📚 Book added successfully!\n\nBook ID: {book_id}\nTitle: {title}")
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add book:\n{str(e)}")


class ModernMemberDialog(ModernDialog):
    """Modern dialog for adding members"""
    def __init__(self, parent, library, title):
        self.library = library
        super().__init__(parent, title)
        self.dialog.geometry("500x550")
        self.create_widgets()
    
    def create_widgets(self):
        """Create member dialog widgets"""
        # Header
        self.create_header("Add New Member", "👤")
        
        # Form container
        form_container = tk.Frame(self.dialog, bg=self.colors['white'])
        form_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)
        
        # Form fields
        self.name_entry = self.create_form_field(form_container, "Full Name", required=True)
        self.email_entry = self.create_form_field(form_container, "Email Address", required=True)
        self.phone_entry = self.create_form_field(form_container, "Phone Number")
        self.address_entry = self.create_form_field(form_container, "Address", "text")
        
        # Buttons
        self.create_buttons(form_container, "👤 Add Member", self.add_member)
    
    def add_member(self):
        """Add the member"""
        try:
            name = self.name_entry.get().strip()
            email = self.email_entry.get().strip()
            phone = self.phone_entry.get().strip()
            address = self.address_entry.get('1.0', tk.END).strip()
            
            # Validate required fields
            if not all([name, email]):
                messagebox.showerror("Validation Error", 
                                   "Please fill in all required fields (marked with *)")
                return
            
            # Add member
            member_id = self.library.add_member(name, email, phone, address)
            self.result = {'id': member_id, 'name': name}
            
            messagebox.showinfo("Success", f"👤 Member added successfully!\n\nMember ID: {member_id}\nName: {name}")
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add member:\n{str(e)}")


class ModernBorrowDialog(ModernDialog):
    """Modern dialog for borrowing books"""
    def __init__(self, parent, library, title):
        self.library = library
        super().__init__(parent, title)
        self.dialog.geometry("500x400")
        self.create_widgets()
    
    def create_widgets(self):
        """Create borrow dialog widgets"""
        # Header
        self.create_header("Borrow Book", "📚")
        
        # Form container
        form_container = tk.Frame(self.dialog, bg=self.colors['white'])
        form_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)
        
        # Form fields
        self.book_id_entry = self.create_form_field(form_container, "Book ID", required=True)
        self.member_id_entry = self.create_form_field(form_container, "Member ID", required=True)
        self.due_days_entry = self.create_form_field(form_container, "Due Days", "spinbox",
                                                    from_=1, to=90, value=14)
        
        # Buttons
        self.create_buttons(form_container, "📚 Borrow Book", self.borrow_book)
    
    def borrow_book(self):
        """Process book borrowing"""
        try:
            book_id = self.book_id_entry.get().strip()
            member_id = self.member_id_entry.get().strip()
            due_days = self.due_days_entry.get().strip()
            
            # Validate required fields
            if not all([book_id, member_id]):
                messagebox.showerror("Validation Error", 
                                   "Please fill in all required fields (marked with *)")
                return
            
            # Convert to integers
            book_id = int(book_id)
            member_id = int(member_id)
            due_days = int(due_days) if due_days else 14
            
            # Borrow book
            transaction_id = self.library.borrow_book(book_id, member_id, due_days)
            self.result = transaction_id
            
            messagebox.showinfo("Success", f"📚 Book borrowed successfully!\n\nTransaction ID: {transaction_id}")
            self.dialog.destroy()
            
        except ValueError:
            messagebox.showerror("Validation Error", "Book ID and Member ID must be numbers!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to borrow book:\n{str(e)}")


class ModernReturnDialog(ModernDialog):
    """Modern dialog for returning books"""
    def __init__(self, parent, library, title):
        self.library = library
        super().__init__(parent, title)
        self.dialog.geometry("500x350")
        self.create_widgets()
    
    def create_widgets(self):
        """Create return dialog widgets"""
        # Header
        self.create_header("Return Book", "↩️")
        
        # Form container
        form_container = tk.Frame(self.dialog, bg=self.colors['white'])
        form_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)
        
        # Form fields
        self.book_id_entry = self.create_form_field(form_container, "Book ID", required=True)
        self.member_id_entry = self.create_form_field(form_container, "Member ID", required=True)
        
        # Buttons
        self.create_buttons(form_container, "↩️ Return Book", self.return_book)
    
    def return_book(self):
        """Process book return"""
        try:
            book_id = self.book_id_entry.get().strip()
            member_id = self.member_id_entry.get().strip()
            
            # Validate required fields
            if not all([book_id, member_id]):
                messagebox.showerror("Validation Error", 
                                   "Please fill in all required fields (marked with *)")
                return
            
            # Convert to integers
            book_id = int(book_id)
            member_id = int(member_id)
            
            # Return book
            success = self.library.return_book(book_id, member_id)
            if success:
                self.result = True
                messagebox.showinfo("Success", "↩️ Book returned successfully!")
                self.dialog.destroy()
            
        except ValueError:
            messagebox.showerror("Validation Error", "Book ID and Member ID must be numbers!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to return book:\n{str(e)}")
