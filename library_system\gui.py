import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from .library import Library
from config import GUI_CONFIG
import threading

class LibraryGUI:
    def __init__(self, root):
        self.root = root
        self.library = Library()
        self.setup_window()
        self.create_widgets()
        self.refresh_data()
    
    def setup_window(self):
        """Setup main window properties"""
        self.root.title(GUI_CONFIG['window_title'])
        self.root.geometry(GUI_CONFIG['window_size'])
        self.root.configure(bg=GUI_CONFIG['bg_color'])
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_width() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_height() // 2)
        self.root.geometry(f"+{x}+{y}")
    
    def create_widgets(self):
        """Create and arrange GUI widgets"""
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Library Management System", 
                               font=(GUI_CONFIG['font_family'], 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Left panel - Controls
        self.create_control_panel(main_frame)
        
        # Right panel - Data display
        self.create_data_panel(main_frame)
        
        # Status bar
        self.create_status_bar(main_frame)
    
    def create_control_panel(self, parent):
        """Create control panel with buttons and forms"""
        control_frame = ttk.LabelFrame(parent, text="Controls", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # Book management section
        book_frame = ttk.LabelFrame(control_frame, text="Book Management", padding="5")
        book_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(book_frame, text="Add Book", command=self.add_book_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(book_frame, text="Search Books", command=self.search_books_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(book_frame, text="Delete Book", command=self.delete_book_dialog).pack(fill=tk.X, pady=2)
        
        # Member management section
        member_frame = ttk.LabelFrame(control_frame, text="Member Management", padding="5")
        member_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(member_frame, text="Add Member", command=self.add_member_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(member_frame, text="Search Members", command=self.search_members_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(member_frame, text="Delete Member", command=self.delete_member_dialog).pack(fill=tk.X, pady=2)
        
        # Transaction section
        transaction_frame = ttk.LabelFrame(control_frame, text="Transactions", padding="5")
        transaction_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(transaction_frame, text="Borrow Book", command=self.borrow_book_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(transaction_frame, text="Return Book", command=self.return_book_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(transaction_frame, text="View Overdue", command=self.view_overdue_books).pack(fill=tk.X, pady=2)
        
        # Refresh button
        ttk.Button(control_frame, text="Refresh All", command=self.refresh_data).pack(fill=tk.X, pady=(10, 0))
    
    def create_data_panel(self, parent):
        """Create data display panel with tabs"""
        self.notebook = ttk.Notebook(parent)
        self.notebook.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Books tab
        self.books_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.books_frame, text="Books")
        self.create_books_table()
        
        # Members tab
        self.members_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.members_frame, text="Members")
        self.create_members_table()
        
        # Transactions tab
        self.transactions_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.transactions_frame, text="Transactions")
        self.create_transactions_table()
        
        # Statistics tab
        self.stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.stats_frame, text="Statistics")
        self.create_statistics_panel()
    
    def create_books_table(self):
        """Create books table"""
        # Create treeview
        columns = ('ID', 'Title', 'Author', 'ISBN', 'Year', 'Category', 'Available')
        self.books_tree = ttk.Treeview(self.books_frame, columns=columns, show='headings', height=15)
        
        # Define headings
        for col in columns:
            self.books_tree.heading(col, text=col)
            self.books_tree.column(col, width=100)
        
        # Add scrollbar
        books_scrollbar = ttk.Scrollbar(self.books_frame, orient=tk.VERTICAL, command=self.books_tree.yview)
        self.books_tree.configure(yscrollcommand=books_scrollbar.set)
        
        # Pack widgets
        self.books_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        books_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_members_table(self):
        """Create members table"""
        # Create treeview
        columns = ('ID', 'Name', 'Email', 'Phone', 'Status', 'Joined Date')
        self.members_tree = ttk.Treeview(self.members_frame, columns=columns, show='headings', height=15)
        
        # Define headings
        for col in columns:
            self.members_tree.heading(col, text=col)
            self.members_tree.column(col, width=120)
        
        # Add scrollbar
        members_scrollbar = ttk.Scrollbar(self.members_frame, orient=tk.VERTICAL, command=self.members_tree.yview)
        self.members_tree.configure(yscrollcommand=members_scrollbar.set)
        
        # Pack widgets
        self.members_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        members_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_transactions_table(self):
        """Create transactions table"""
        # Create treeview
        columns = ('ID', 'Book Title', 'Member Name', 'Type', 'Date', 'Due Date', 'Status')
        self.transactions_tree = ttk.Treeview(self.transactions_frame, columns=columns, show='headings', height=15)
        
        # Define headings
        for col in columns:
            self.transactions_tree.heading(col, text=col)
            self.transactions_tree.column(col, width=120)
        
        # Add scrollbar
        transactions_scrollbar = ttk.Scrollbar(self.transactions_frame, orient=tk.VERTICAL, command=self.transactions_tree.yview)
        self.transactions_tree.configure(yscrollcommand=transactions_scrollbar.set)
        
        # Pack widgets
        self.transactions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        transactions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_statistics_panel(self):
        """Create statistics panel"""
        stats_label_frame = ttk.LabelFrame(self.stats_frame, text="Library Statistics", padding="10")
        stats_label_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.stats_labels = {}
        stats_items = [
            ('Total Books', 'total_books'),
            ('Available Books', 'available_books'),
            ('Borrowed Books', 'borrowed_books'),
            ('Total Members', 'total_members'),
            ('Active Members', 'active_members'),
            ('Overdue Books', 'overdue_books')
        ]
        
        for i, (label_text, key) in enumerate(stats_items):
            row = i // 2
            col = i % 2
            
            frame = ttk.Frame(stats_label_frame)
            frame.grid(row=row, column=col, padx=20, pady=10, sticky=tk.W)
            
            ttk.Label(frame, text=f"{label_text}:", font=(GUI_CONFIG['font_family'], 12, 'bold')).pack(anchor=tk.W)
            self.stats_labels[key] = ttk.Label(frame, text="0", font=(GUI_CONFIG['font_family'], 14))
            self.stats_labels[key].pack(anchor=tk.W)
    
    def create_status_bar(self, parent):
        """Create status bar"""
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(parent, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def update_status(self, message):
        """Update status bar message"""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def refresh_data(self):
        """Refresh all data in the GUI"""
        self.update_status("Refreshing data...")
        try:
            self.load_books()
            self.load_members()
            self.load_transactions()
            self.load_statistics()
            self.update_status("Data refreshed successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh data: {str(e)}")
            self.update_status("Error refreshing data")
    
    def load_books(self):
        """Load books into the table"""
        # Clear existing data
        for item in self.books_tree.get_children():
            self.books_tree.delete(item)
        
        # Load new data
        books = self.library.get_all_books()
        for book in books:
            self.books_tree.insert('', tk.END, values=(
                book['book_id'],
                book['title'],
                book['author'],
                book['isbn'],
                book['published_year'] or '',
                book['category'],
                'Yes' if book['available'] else 'No'
            ))
    
    def load_members(self):
        """Load members into the table"""
        # Clear existing data
        for item in self.members_tree.get_children():
            self.members_tree.delete(item)
        
        # Load new data
        members = self.library.get_all_members()
        for member in members:
            self.members_tree.insert('', tk.END, values=(
                member['member_id'],
                member['name'],
                member['email'],
                member['phone'] or '',
                member['status'].title(),
                member['joined_date']
            ))
    
    def load_transactions(self):
        """Load transactions into the table"""
        # Clear existing data
        for item in self.transactions_tree.get_children():
            self.transactions_tree.delete(item)
        
        # Load new data
        transactions = self.library.get_transaction_history(50)
        for transaction in transactions:
            self.transactions_tree.insert('', tk.END, values=(
                transaction['transaction_id'],
                transaction['title'],
                transaction['member_name'],
                transaction['transaction_type'].title(),
                transaction['transaction_date'],
                transaction['due_date'] or '',
                transaction['status'].title()
            ))
    
    def load_statistics(self):
        """Load statistics"""
        stats = self.library.get_library_stats()
        for key, label in self.stats_labels.items():
            label.config(text=str(stats.get(key, 0)))

    # Dialog methods
    def add_book_dialog(self):
        """Show add book dialog"""
        dialog = AddBookDialog(self.root, self.library)
        if dialog.result:
            self.load_books()
            self.load_statistics()
            self.update_status("Book added successfully")

    def add_member_dialog(self):
        """Show add member dialog"""
        dialog = AddMemberDialog(self.root, self.library)
        if dialog.result:
            self.load_members()
            self.load_statistics()
            self.update_status("Member added successfully")

    def search_books_dialog(self):
        """Show search books dialog"""
        search_term = simpledialog.askstring("Search Books", "Enter search term (title, author, or ISBN):")
        if search_term:
            try:
                books = self.library.search_books(search_term)
                self.show_search_results("Books", books, ['book_id', 'title', 'author', 'isbn', 'available'])
            except Exception as e:
                messagebox.showerror("Error", f"Search failed: {str(e)}")

    def search_members_dialog(self):
        """Show search members dialog"""
        search_term = simpledialog.askstring("Search Members", "Enter search term (name or email):")
        if search_term:
            try:
                members = self.library.search_members(search_term)
                self.show_search_results("Members", members, ['member_id', 'name', 'email', 'status'])
            except Exception as e:
                messagebox.showerror("Error", f"Search failed: {str(e)}")

    def delete_book_dialog(self):
        """Show delete book dialog"""
        book_id = simpledialog.askinteger("Delete Book", "Enter Book ID to delete:")
        if book_id:
            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete book ID {book_id}?"):
                try:
                    self.library.delete_book(book_id)
                    self.load_books()
                    self.load_statistics()
                    self.update_status("Book deleted successfully")
                except Exception as e:
                    messagebox.showerror("Error", f"Delete failed: {str(e)}")

    def delete_member_dialog(self):
        """Show delete member dialog"""
        member_id = simpledialog.askinteger("Delete Member", "Enter Member ID to delete:")
        if member_id:
            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete member ID {member_id}?"):
                try:
                    self.library.delete_member(member_id)
                    self.load_members()
                    self.load_statistics()
                    self.update_status("Member deleted successfully")
                except Exception as e:
                    messagebox.showerror("Error", f"Delete failed: {str(e)}")

    def borrow_book_dialog(self):
        """Show borrow book dialog"""
        dialog = BorrowBookDialog(self.root, self.library)
        if dialog.result:
            self.load_books()
            self.load_transactions()
            self.load_statistics()
            self.update_status("Book borrowed successfully")

    def return_book_dialog(self):
        """Show return book dialog"""
        dialog = ReturnBookDialog(self.root, self.library)
        if dialog.result:
            self.load_books()
            self.load_transactions()
            self.load_statistics()
            self.update_status("Book returned successfully")

    def view_overdue_books(self):
        """Show overdue books"""
        try:
            overdue_books = self.library.get_overdue_books()
            if overdue_books:
                self.show_search_results("Overdue Books", overdue_books,
                                       ['transaction_id', 'title', 'member_name', 'due_date'])
            else:
                messagebox.showinfo("Overdue Books", "No overdue books found!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to get overdue books: {str(e)}")

    def show_search_results(self, title, results, columns):
        """Show search results in a new window"""
        if not results:
            messagebox.showinfo("Search Results", "No results found!")
            return

        # Create new window
        result_window = tk.Toplevel(self.root)
        result_window.title(f"{title} - Search Results")
        result_window.geometry("800x400")

        # Create treeview
        tree = ttk.Treeview(result_window, columns=columns, show='headings')

        # Define headings
        for col in columns:
            tree.heading(col, text=col.replace('_', ' ').title())
            tree.column(col, width=100)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(result_window, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # Insert data
        for item in results:
            values = [item.get(col, '') for col in columns]
            tree.insert('', tk.END, values=values)

        # Pack widgets
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)


class AddBookDialog:
    def __init__(self, parent, library):
        self.library = library
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Add New Book")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

        self.create_widgets()

    def create_widgets(self):
        """Create dialog widgets"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text="Add New Book", font=('Arial', 14, 'bold')).pack(pady=(0, 20))

        # Form fields
        self.entries = {}
        fields = [
            ('Title*', 'title'),
            ('Author*', 'author'),
            ('ISBN*', 'isbn'),
            ('Published Year', 'published_year'),
            ('Category', 'category')
        ]

        for label_text, field_name in fields:
            frame = ttk.Frame(main_frame)
            frame.pack(fill=tk.X, pady=5)

            ttk.Label(frame, text=label_text, width=15).pack(side=tk.LEFT)
            entry = ttk.Entry(frame, width=30)
            entry.pack(side=tk.LEFT, padx=(10, 0))
            self.entries[field_name] = entry

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(20, 0))

        ttk.Button(button_frame, text="Add Book", command=self.add_book).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.LEFT)

    def add_book(self):
        """Add the book"""
        try:
            title = self.entries['title'].get().strip()
            author = self.entries['author'].get().strip()
            isbn = self.entries['isbn'].get().strip()
            published_year = self.entries['published_year'].get().strip()
            category = self.entries['category'].get().strip() or "General"

            # Validate required fields
            if not all([title, author, isbn]):
                messagebox.showerror("Error", "Title, Author, and ISBN are required!")
                return

            # Convert year to integer if provided
            year = None
            if published_year:
                try:
                    year = int(published_year)
                except ValueError:
                    messagebox.showerror("Error", "Published year must be a number!")
                    return

            # Add book
            book_id = self.library.add_book(title, author, isbn, year, category)
            self.result = book_id
            messagebox.showinfo("Success", f"Book added successfully! ID: {book_id}")
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to add book: {str(e)}")


class AddMemberDialog:
    def __init__(self, parent, library):
        self.library = library
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Add New Member")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

        self.create_widgets()

    def create_widgets(self):
        """Create dialog widgets"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text="Add New Member", font=('Arial', 14, 'bold')).pack(pady=(0, 20))

        # Form fields
        self.entries = {}
        fields = [
            ('Name*', 'name'),
            ('Email*', 'email'),
            ('Phone', 'phone'),
            ('Address', 'address')
        ]

        for label_text, field_name in fields:
            frame = ttk.Frame(main_frame)
            frame.pack(fill=tk.X, pady=5)

            ttk.Label(frame, text=label_text, width=15).pack(side=tk.LEFT)
            if field_name == 'address':
                entry = tk.Text(frame, width=30, height=3)
            else:
                entry = ttk.Entry(frame, width=30)
            entry.pack(side=tk.LEFT, padx=(10, 0))
            self.entries[field_name] = entry

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(20, 0))

        ttk.Button(button_frame, text="Add Member", command=self.add_member).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.LEFT)

    def add_member(self):
        """Add the member"""
        try:
            name = self.entries['name'].get().strip()
            email = self.entries['email'].get().strip()
            phone = self.entries['phone'].get().strip()
            address = self.entries['address'].get('1.0', tk.END).strip()

            # Validate required fields
            if not all([name, email]):
                messagebox.showerror("Error", "Name and Email are required!")
                return

            # Add member
            member_id = self.library.add_member(name, email, phone, address)
            self.result = member_id
            messagebox.showinfo("Success", f"Member added successfully! ID: {member_id}")
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to add member: {str(e)}")


class BorrowBookDialog:
    def __init__(self, parent, library):
        self.library = library
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Borrow Book")
        self.dialog.geometry("400x200")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

        self.create_widgets()

    def create_widgets(self):
        """Create dialog widgets"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text="Borrow Book", font=('Arial', 14, 'bold')).pack(pady=(0, 20))

        # Form fields
        self.entries = {}
        fields = [
            ('Book ID*', 'book_id'),
            ('Member ID*', 'member_id'),
            ('Due Days (default: 14)', 'due_days')
        ]

        for label_text, field_name in fields:
            frame = ttk.Frame(main_frame)
            frame.pack(fill=tk.X, pady=5)

            ttk.Label(frame, text=label_text, width=20).pack(side=tk.LEFT)
            entry = ttk.Entry(frame, width=20)
            entry.pack(side=tk.LEFT, padx=(10, 0))
            self.entries[field_name] = entry

        # Set default due days
        self.entries['due_days'].insert(0, "14")

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(20, 0))

        ttk.Button(button_frame, text="Borrow", command=self.borrow_book).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.LEFT)

    def borrow_book(self):
        """Process book borrowing"""
        try:
            book_id = self.entries['book_id'].get().strip()
            member_id = self.entries['member_id'].get().strip()
            due_days = self.entries['due_days'].get().strip() or "14"

            # Validate required fields
            if not all([book_id, member_id]):
                messagebox.showerror("Error", "Book ID and Member ID are required!")
                return

            # Convert to integers
            try:
                book_id = int(book_id)
                member_id = int(member_id)
                due_days = int(due_days)
            except ValueError:
                messagebox.showerror("Error", "IDs and due days must be numbers!")
                return

            # Borrow book
            transaction_id = self.library.borrow_book(book_id, member_id, due_days)
            self.result = transaction_id
            messagebox.showinfo("Success", f"Book borrowed successfully! Transaction ID: {transaction_id}")
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to borrow book: {str(e)}")


class ReturnBookDialog:
    def __init__(self, parent, library):
        self.library = library
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Return Book")
        self.dialog.geometry("400x150")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

        self.create_widgets()

    def create_widgets(self):
        """Create dialog widgets"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text="Return Book", font=('Arial', 14, 'bold')).pack(pady=(0, 20))

        # Form fields
        self.entries = {}
        fields = [
            ('Book ID*', 'book_id'),
            ('Member ID*', 'member_id')
        ]

        for label_text, field_name in fields:
            frame = ttk.Frame(main_frame)
            frame.pack(fill=tk.X, pady=5)

            ttk.Label(frame, text=label_text, width=15).pack(side=tk.LEFT)
            entry = ttk.Entry(frame, width=20)
            entry.pack(side=tk.LEFT, padx=(10, 0))
            self.entries[field_name] = entry

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(20, 0))

        ttk.Button(button_frame, text="Return", command=self.return_book).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.LEFT)

    def return_book(self):
        """Process book return"""
        try:
            book_id = self.entries['book_id'].get().strip()
            member_id = self.entries['member_id'].get().strip()

            # Validate required fields
            if not all([book_id, member_id]):
                messagebox.showerror("Error", "Book ID and Member ID are required!")
                return

            # Convert to integers
            try:
                book_id = int(book_id)
                member_id = int(member_id)
            except ValueError:
                messagebox.showerror("Error", "IDs must be numbers!")
                return

            # Return book
            success = self.library.return_book(book_id, member_id)
            if success:
                self.result = True
                messagebox.showinfo("Success", "Book returned successfully!")
                self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to return book: {str(e)}")
