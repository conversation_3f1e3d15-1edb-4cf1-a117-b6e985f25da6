import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from .library import Library
from .modern_dialogs import ModernBookDialog, ModernMemberDialog, ModernBorrowDialog, ModernReturnDialog
from config import GUI_CONFIG
import datetime

class ModernLibraryGUI:
    def __init__(self, root):
        self.root = root
        self.library = Library()
        self.setup_modern_style()
        self.setup_window()
        self.create_modern_widgets()
        self.refresh_data()

    def setup_modern_style(self):
        """Setup modern styling"""
        self.style = ttk.Style()

        # Configure modern theme
        self.style.theme_use('clam')

        # Define color scheme
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#28A745',
            'warning': '#FFC107',
            'danger': '#DC3545',
            'light': '#F8F9FA',
            'dark': '#343A40',
            'white': '#FFFFFF',
            'gray': '#6C757D'
        }

        # Configure styles
        self.style.configure('Title.TLabel',
                           font=('Segoe UI', 24, 'bold'),
                           foreground=self.colors['primary'],
                           background=self.colors['light'])

        self.style.configure('Heading.TLabel',
                           font=('Segoe UI', 14, 'bold'),
                           foreground=self.colors['dark'])

        self.style.configure('Modern.TButton',
                           font=('Segoe UI', 10),
                           padding=(10, 5))

        self.style.configure('Primary.TButton',
                           font=('Segoe UI', 10, 'bold'),
                           foreground=self.colors['white'])

        self.style.map('Primary.TButton',
                      background=[('active', self.colors['primary']),
                                ('!active', self.colors['primary'])])

        self.style.configure('Success.TButton',
                           font=('Segoe UI', 10),
                           foreground=self.colors['white'])

        self.style.map('Success.TButton',
                      background=[('active', self.colors['success']),
                                ('!active', self.colors['success'])])

        self.style.configure('Danger.TButton',
                           font=('Segoe UI', 10),
                           foreground=self.colors['white'])

        self.style.map('Danger.TButton',
                      background=[('active', self.colors['danger']),
                                ('!active', self.colors['danger'])])

    def setup_window(self):
        """Setup main window properties"""
        self.root.title("📚 " + GUI_CONFIG['window_title'])
        self.root.geometry("1400x900")
        self.root.configure(bg=self.colors['light'])
        self.root.state('zoomed')  # Maximize window

        # Set minimum size
        self.root.minsize(1200, 800)

        # Center window
        self.center_window()

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def create_modern_widgets(self):
        """Create modern GUI layout"""
        # Main container
        self.main_container = tk.Frame(self.root, bg=self.colors['light'])
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # Header
        self.create_header()

        # Main content area
        self.content_frame = tk.Frame(self.main_container, bg=self.colors['light'])
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Create dashboard and main content
        self.create_dashboard()
        self.create_main_content()

        # Footer/Status bar
        self.create_footer()

    def create_header(self):
        """Create modern header with title and quick stats"""
        header_frame = tk.Frame(self.main_container, bg=self.colors['primary'], height=100)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # Title section
        title_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        title_frame.pack(side=tk.LEFT, fill=tk.Y, padx=30, pady=20)

        title_label = tk.Label(title_frame,
                              text="📚 Library Management System",
                              font=('Segoe UI', 28, 'bold'),
                              fg=self.colors['white'],
                              bg=self.colors['primary'])
        title_label.pack(anchor=tk.W)

        subtitle_label = tk.Label(title_frame,
                                 text=f"Welcome! Today is {datetime.datetime.now().strftime('%B %d, %Y')}",
                                 font=('Segoe UI', 12),
                                 fg=self.colors['light'],
                                 bg=self.colors['primary'])
        subtitle_label.pack(anchor=tk.W)

        # Quick stats section
        self.stats_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        self.stats_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=30, pady=20)

        self.create_quick_stats()

    def create_quick_stats(self):
        """Create quick statistics cards in header"""
        self.quick_stats = {}
        stats_data = [
            ("📚", "Total Books", "total_books", self.colors['white']),
            ("✅", "Available", "available_books", self.colors['success']),
            ("👥", "Members", "total_members", self.colors['warning']),
            ("⚠️", "Overdue", "overdue_books", self.colors['danger'])
        ]

        for i, (icon, label, key, color) in enumerate(stats_data):
            stat_frame = tk.Frame(self.stats_frame, bg=self.colors['primary'])
            stat_frame.grid(row=0, column=i, padx=15)

            icon_label = tk.Label(stat_frame, text=icon, font=('Segoe UI', 20),
                                 fg=color, bg=self.colors['primary'])
            icon_label.pack()

            value_label = tk.Label(stat_frame, text="0", font=('Segoe UI', 16, 'bold'),
                                  fg=self.colors['white'], bg=self.colors['primary'])
            value_label.pack()

            text_label = tk.Label(stat_frame, text=label, font=('Segoe UI', 10),
                                 fg=self.colors['light'], bg=self.colors['primary'])
            text_label.pack()

            self.quick_stats[key] = value_label

    def create_dashboard(self):
        """Create dashboard with action cards"""
        dashboard_frame = tk.Frame(self.content_frame, bg=self.colors['light'])
        dashboard_frame.pack(fill=tk.X, pady=(0, 20))

        # Dashboard title
        dash_title = tk.Label(dashboard_frame, text="Quick Actions",
                             font=('Segoe UI', 18, 'bold'),
                             fg=self.colors['dark'], bg=self.colors['light'])
        dash_title.pack(anchor=tk.W, pady=(0, 15))

        # Action cards container
        cards_frame = tk.Frame(dashboard_frame, bg=self.colors['light'])
        cards_frame.pack(fill=tk.X)

        # Action cards
        self.create_action_cards(cards_frame)

    def create_action_cards(self, parent):
        """Create modern action cards"""
        actions = [
            ("📖", "Add Book", "Add new books to library", self.add_book_dialog, self.colors['primary']),
            ("👤", "Add Member", "Register new members", self.add_member_dialog, self.colors['secondary']),
            ("📚", "Borrow Book", "Issue books to members", self.borrow_book_dialog, self.colors['success']),
            ("↩️", "Return Book", "Process book returns", self.return_book_dialog, self.colors['warning']),
            ("🔍", "Search Books", "Find books in library", self.search_books_dialog, self.colors['gray']),
            ("⚠️", "View Overdue", "Check overdue books", self.view_overdue_books, self.colors['danger'])
        ]

        for i, (icon, title, desc, command, color) in enumerate(actions):
            card = self.create_action_card(parent, icon, title, desc, command, color)
            card.grid(row=i//3, column=i%3, padx=10, pady=10, sticky='ew')

        # Configure grid weights
        for i in range(3):
            parent.columnconfigure(i, weight=1)

    def create_action_card(self, parent, icon, title, description, command, color):
        """Create individual action card"""
        card_frame = tk.Frame(parent, bg=self.colors['white'], relief=tk.RAISED, bd=1)

        # Card content
        content_frame = tk.Frame(card_frame, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)

        # Icon
        icon_label = tk.Label(content_frame, text=icon, font=('Segoe UI', 24),
                             fg=color, bg=self.colors['white'])
        icon_label.pack()

        # Title
        title_label = tk.Label(content_frame, text=title, font=('Segoe UI', 14, 'bold'),
                              fg=self.colors['dark'], bg=self.colors['white'])
        title_label.pack(pady=(5, 2))

        # Description
        desc_label = tk.Label(content_frame, text=description, font=('Segoe UI', 10),
                             fg=self.colors['gray'], bg=self.colors['white'])
        desc_label.pack(pady=(0, 10))

        # Button
        btn = tk.Button(content_frame, text=title, font=('Segoe UI', 10, 'bold'),
                       fg=self.colors['white'], bg=color, relief=tk.FLAT,
                       command=command, cursor='hand2', padx=20, pady=8)
        btn.pack()

        # Hover effects
        def on_enter(e):
            card_frame.configure(relief=tk.RAISED, bd=2)
            btn.configure(bg=self.darken_color(color))

        def on_leave(e):
            card_frame.configure(relief=tk.RAISED, bd=1)
            btn.configure(bg=color)

        card_frame.bind("<Enter>", on_enter)
        card_frame.bind("<Leave>", on_leave)
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)

        return card_frame

    def darken_color(self, color):
        """Darken a color for hover effect"""
        color_map = {
            self.colors['primary']: '#1e5f7a',
            self.colors['secondary']: '#7a2a54',
            self.colors['success']: '#1e7e34',
            self.colors['warning']: '#d39e00',
            self.colors['danger']: '#bd2130',
            self.colors['gray']: '#545b62'
        }
        return color_map.get(color, color)

    def create_main_content(self):
        """Create main content area with tabs"""
        content_container = tk.Frame(self.content_frame, bg=self.colors['light'])
        content_container.pack(fill=tk.BOTH, expand=True)

        # Content title
        content_title = tk.Label(content_container, text="Library Data",
                                font=('Segoe UI', 18, 'bold'),
                                fg=self.colors['dark'], bg=self.colors['light'])
        content_title.pack(anchor=tk.W, pady=(0, 15))

        # Create notebook with modern styling
        self.notebook = ttk.Notebook(content_container)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Configure notebook style
        self.style.configure('TNotebook', background=self.colors['light'])
        self.style.configure('TNotebook.Tab', padding=[20, 10])

        # Create tabs
        self.create_modern_tabs()

    def create_modern_tabs(self):
        """Create modern styled tabs"""
        # Books tab
        self.books_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.books_frame, text="📚 Books")
        self.create_modern_books_table()

        # Members tab
        self.members_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.members_frame, text="👥 Members")
        self.create_modern_members_table()

        # Transactions tab
        self.transactions_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.transactions_frame, text="📋 Transactions")
        self.create_modern_transactions_table()

    def create_modern_books_table(self):
        """Create modern books table with search and filters"""
        # Main container with padding
        main_container = tk.Frame(self.books_frame, bg=self.colors['light'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Search section
        search_section = tk.Frame(main_container, bg=self.colors['white'], relief=tk.RAISED, bd=1)
        search_section.pack(fill=tk.X, pady=(0, 15))

        search_inner = tk.Frame(search_section, bg=self.colors['white'])
        search_inner.pack(fill=tk.X, padx=20, pady=15)

        tk.Label(search_inner, text="🔍 Search Books:", font=('Segoe UI', 12, 'bold'),
                bg=self.colors['white'], fg=self.colors['dark']).pack(side=tk.LEFT)

        self.books_search_var = tk.StringVar()
        search_entry = tk.Entry(search_inner, textvariable=self.books_search_var,
                               font=('Segoe UI', 11), width=40, relief=tk.FLAT, bd=5)
        search_entry.pack(side=tk.LEFT, padx=(15, 0), ipady=5)
        search_entry.bind('<KeyRelease>', self.filter_books)

        # Table section
        table_section = tk.Frame(main_container, bg=self.colors['white'], relief=tk.RAISED, bd=1)
        table_section.pack(fill=tk.BOTH, expand=True)

        # Table container
        table_container = tk.Frame(table_section, bg=self.colors['white'])
        table_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Table
        columns = ('ID', 'Title', 'Author', 'ISBN', 'Year', 'Category', 'Status')
        self.books_tree = ttk.Treeview(table_container, columns=columns, show='headings', height=18)

        # Configure columns with better widths
        column_widths = {'ID': 60, 'Title': 250, 'Author': 180, 'ISBN': 130,
                        'Year': 80, 'Category': 120, 'Status': 100}

        for col in columns:
            self.books_tree.heading(col, text=col, anchor=tk.W)
            self.books_tree.column(col, width=column_widths.get(col, 100), anchor=tk.W)

        # Create frame for table and scrollbars
        tree_frame = tk.Frame(table_container, bg=self.colors['white'])
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.books_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.books_tree.xview)
        self.books_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid layout for proper scrollbar positioning
        self.books_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # Configure grid weights
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # Configure alternating row colors
        self.books_tree.tag_configure('oddrow', background='#f8f9fa')
        self.books_tree.tag_configure('evenrow', background='#ffffff')

    def create_modern_members_table(self):
        """Create modern members table"""
        # Main container with padding
        main_container = tk.Frame(self.members_frame, bg=self.colors['light'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Search section
        search_section = tk.Frame(main_container, bg=self.colors['white'], relief=tk.RAISED, bd=1)
        search_section.pack(fill=tk.X, pady=(0, 15))

        search_inner = tk.Frame(search_section, bg=self.colors['white'])
        search_inner.pack(fill=tk.X, padx=20, pady=15)

        tk.Label(search_inner, text="🔍 Search Members:", font=('Segoe UI', 12, 'bold'),
                bg=self.colors['white'], fg=self.colors['dark']).pack(side=tk.LEFT)

        self.members_search_var = tk.StringVar()
        search_entry = tk.Entry(search_inner, textvariable=self.members_search_var,
                               font=('Segoe UI', 11), width=40, relief=tk.FLAT, bd=5)
        search_entry.pack(side=tk.LEFT, padx=(15, 0), ipady=5)
        search_entry.bind('<KeyRelease>', self.filter_members)

        # Table section
        table_section = tk.Frame(main_container, bg=self.colors['white'], relief=tk.RAISED, bd=1)
        table_section.pack(fill=tk.BOTH, expand=True)

        # Table container
        table_container = tk.Frame(table_section, bg=self.colors['white'])
        table_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Table
        columns = ('ID', 'Name', 'Email', 'Phone', 'Status', 'Joined Date')
        self.members_tree = ttk.Treeview(table_container, columns=columns, show='headings', height=18)

        column_widths = {'ID': 60, 'Name': 180, 'Email': 250, 'Phone': 140,
                        'Status': 100, 'Joined Date': 120}

        for col in columns:
            self.members_tree.heading(col, text=col, anchor=tk.W)
            self.members_tree.column(col, width=column_widths.get(col, 100), anchor=tk.W)

        # Create frame for table and scrollbars
        tree_frame = tk.Frame(table_container, bg=self.colors['white'])
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.members_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.members_tree.xview)
        self.members_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid layout for proper scrollbar positioning
        self.members_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # Configure grid weights
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        self.members_tree.tag_configure('oddrow', background='#f8f9fa')
        self.members_tree.tag_configure('evenrow', background='#ffffff')

    def create_modern_transactions_table(self):
        """Create modern transactions table"""
        # Main container with padding
        main_container = tk.Frame(self.transactions_frame, bg=self.colors['light'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Header section
        header_section = tk.Frame(main_container, bg=self.colors['white'], relief=tk.RAISED, bd=1)
        header_section.pack(fill=tk.X, pady=(0, 15))

        header_inner = tk.Frame(header_section, bg=self.colors['white'])
        header_inner.pack(fill=tk.X, padx=20, pady=15)

        tk.Label(header_inner, text="📋 Transaction History", font=('Segoe UI', 14, 'bold'),
                bg=self.colors['white'], fg=self.colors['dark']).pack(side=tk.LEFT)

        # Legend
        legend_frame = tk.Frame(header_inner, bg=self.colors['white'])
        legend_frame.pack(side=tk.RIGHT)

        legend_items = [
            ("🟡 Active", self.colors['warning']),
            ("🟢 Completed", self.colors['success']),
            ("🔴 Overdue", self.colors['danger'])
        ]

        for text, color in legend_items:
            legend_label = tk.Label(legend_frame, text=text, font=('Segoe UI', 9),
                                   fg=color, bg=self.colors['white'])
            legend_label.pack(side=tk.LEFT, padx=(0, 15))

        # Table section
        table_section = tk.Frame(main_container, bg=self.colors['white'], relief=tk.RAISED, bd=1)
        table_section.pack(fill=tk.BOTH, expand=True)

        # Table container
        table_container = tk.Frame(table_section, bg=self.colors['white'])
        table_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Table
        columns = ('ID', 'Book Title', 'Member Name', 'Type', 'Date', 'Due Date', 'Status')
        self.transactions_tree = ttk.Treeview(table_container, columns=columns, show='headings', height=18)

        column_widths = {'ID': 60, 'Book Title': 250, 'Member Name': 180, 'Type': 80,
                        'Date': 100, 'Due Date': 100, 'Status': 100}

        for col in columns:
            self.transactions_tree.heading(col, text=col, anchor=tk.W)
            self.transactions_tree.column(col, width=column_widths.get(col, 100), anchor=tk.W)

        # Create frame for table and scrollbars
        tree_frame = tk.Frame(table_container, bg=self.colors['white'])
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.transactions_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.transactions_tree.xview)
        self.transactions_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid layout for proper scrollbar positioning
        self.transactions_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # Configure grid weights
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # Color coding for transaction status
        self.transactions_tree.tag_configure('active', background='#fff3cd', foreground='#856404')
        self.transactions_tree.tag_configure('completed', background='#d4edda', foreground='#155724')
        self.transactions_tree.tag_configure('overdue', background='#f8d7da', foreground='#721c24')



    def create_footer(self):
        """Create modern footer with status"""
        footer_frame = tk.Frame(self.main_container, bg=self.colors['dark'], height=40)
        footer_frame.pack(fill=tk.X, side=tk.BOTTOM)
        footer_frame.pack_propagate(False)

        # Status section
        status_frame = tk.Frame(footer_frame, bg=self.colors['dark'])
        status_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20)

        self.status_var = tk.StringVar()
        self.status_var.set("🟢 Ready - Library Management System")

        status_label = tk.Label(status_frame, textvariable=self.status_var,
                               font=('Segoe UI', 10), fg=self.colors['light'],
                               bg=self.colors['dark'])
        status_label.pack(side=tk.LEFT, pady=10)

        # Refresh button in footer
        refresh_frame = tk.Frame(footer_frame, bg=self.colors['dark'])
        refresh_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=20)

        refresh_btn = tk.Button(refresh_frame, text="🔄 Refresh",
                               font=('Segoe UI', 10), fg=self.colors['white'],
                               bg=self.colors['primary'], relief=tk.FLAT,
                               command=self.refresh_data, cursor='hand2',
                               padx=15, pady=5)
        refresh_btn.pack(pady=7)

    def filter_books(self, event=None):
        """Filter books based on search"""
        search_term = self.books_search_var.get().lower()

        # Clear current items
        for item in self.books_tree.get_children():
            self.books_tree.delete(item)

        # Get all books and filter
        books = self.library.get_all_books()
        filtered_books = []

        for book in books:
            if (search_term in book['title'].lower() or
                search_term in book['author'].lower() or
                search_term in book['isbn'].lower()):
                filtered_books.append(book)

        # Display filtered books
        for i, book in enumerate(filtered_books):
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            self.books_tree.insert('', tk.END, values=(
                book['book_id'], book['title'], book['author'], book['isbn'],
                book['published_year'] or '', book['category'],
                '✅ Available' if book['available'] else '❌ Borrowed'
            ), tags=(tag,))

    def filter_members(self, event=None):
        """Filter members based on search"""
        search_term = self.members_search_var.get().lower()

        # Clear current items
        for item in self.members_tree.get_children():
            self.members_tree.delete(item)

        # Get all members and filter
        members = self.library.get_all_members()
        filtered_members = []

        for member in members:
            if (search_term in member['name'].lower() or
                search_term in member['email'].lower()):
                filtered_members.append(member)

        # Display filtered members
        for i, member in enumerate(filtered_members):
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            status_icon = '🟢' if member['status'] == 'active' else '🔴'
            self.members_tree.insert('', tk.END, values=(
                member['member_id'], member['name'], member['email'],
                member['phone'] or '', f"{status_icon} {member['status'].title()}",
                member['joined_date']
            ), tags=(tag,))

    def update_status(self, message, status_type="info"):
        """Update status bar with colored status"""
        status_icons = {
            "info": "🟢",
            "warning": "🟡",
            "error": "🔴",
            "success": "✅"
        }

        icon = status_icons.get(status_type, "🟢")
        self.status_var.set(f"{icon} {message}")
        self.root.update_idletasks()

    def refresh_data(self):
        """Refresh all data with modern loading indication"""
        self.update_status("Refreshing data...", "info")
        try:
            self.load_books()
            self.load_members()
            self.load_transactions()
            self.load_statistics()
            self.update_status("Data refreshed successfully", "success")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh data: {str(e)}")
            self.update_status("Error refreshing data", "error")

    def load_books(self):
        """Load books with modern styling"""
        # Clear existing data
        for item in self.books_tree.get_children():
            self.books_tree.delete(item)

        # Load new data
        books = self.library.get_all_books()
        for i, book in enumerate(books):
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            status = '✅ Available' if book['available'] else '❌ Borrowed'
            self.books_tree.insert('', tk.END, values=(
                book['book_id'], book['title'], book['author'], book['isbn'],
                book['published_year'] or '', book['category'], status
            ), tags=(tag,))

    def load_members(self):
        """Load members with modern styling"""
        for item in self.members_tree.get_children():
            self.members_tree.delete(item)

        members = self.library.get_all_members()
        for i, member in enumerate(members):
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            status_icon = '🟢' if member['status'] == 'active' else '🔴'
            self.members_tree.insert('', tk.END, values=(
                member['member_id'], member['name'], member['email'],
                member['phone'] or '', f"{status_icon} {member['status'].title()}",
                member['joined_date']
            ), tags=(tag,))

    def load_transactions(self):
        """Load transactions with color coding"""
        for item in self.transactions_tree.get_children():
            self.transactions_tree.delete(item)

        transactions = self.library.get_transaction_history(50)
        for transaction in transactions:
            # Determine tag based on status
            tag = transaction['status']

            self.transactions_tree.insert('', tk.END, values=(
                transaction['transaction_id'], transaction['title'],
                transaction['member_name'], transaction['transaction_type'].title(),
                transaction['transaction_date'], transaction['due_date'] or '',
                transaction['status'].title()
            ), tags=(tag,))

    def load_statistics(self):
        """Load statistics for quick stats in header"""
        stats = self.library.get_library_stats()

        # Update quick stats in header
        for key, label in self.quick_stats.items():
            label.config(text=str(stats.get(key, 0)))

    # Modern Dialog Methods
    def add_book_dialog(self):
        """Show modern add book dialog"""
        dialog = ModernBookDialog(self.root, self.library, "Add New Book")
        if dialog.result:
            self.refresh_data()
            self.update_status(f"Book '{dialog.result['title']}' added successfully", "success")

    def add_member_dialog(self):
        """Show modern add member dialog"""
        dialog = ModernMemberDialog(self.root, self.library, "Add New Member")
        if dialog.result:
            self.refresh_data()
            self.update_status(f"Member '{dialog.result['name']}' added successfully", "success")

    def search_books_dialog(self):
        """Show modern search books dialog"""
        search_term = simpledialog.askstring("🔍 Search Books",
                                            "Enter search term (title, author, or ISBN):",
                                            parent=self.root)
        if search_term:
            try:
                books = self.library.search_books(search_term)
                self.show_modern_search_results("Books", books, search_term)
            except Exception as e:
                messagebox.showerror("Error", f"Search failed: {str(e)}")

    def search_members_dialog(self):
        """Show modern search members dialog"""
        search_term = simpledialog.askstring("🔍 Search Members",
                                            "Enter search term (name or email):",
                                            parent=self.root)
        if search_term:
            try:
                members = self.library.search_members(search_term)
                self.show_modern_search_results("Members", members, search_term)
            except Exception as e:
                messagebox.showerror("Error", f"Search failed: {str(e)}")

    def delete_book_dialog(self):
        """Show modern delete book dialog"""
        book_id = simpledialog.askinteger("🗑️ Delete Book", "Enter Book ID to delete:",
                                         parent=self.root)
        if book_id and messagebox.askyesno("Confirm Delete",
                                          f"Are you sure you want to delete book ID {book_id}?\n\nThis action cannot be undone.",
                                          parent=self.root):
            try:
                self.library.delete_book(book_id)
                self.refresh_data()
                self.update_status(f"Book ID {book_id} deleted successfully", "success")
            except Exception as e:
                messagebox.showerror("Error", f"Delete failed: {str(e)}")
                self.update_status("Failed to delete book", "error")

    def delete_member_dialog(self):
        """Show modern delete member dialog"""
        member_id = simpledialog.askinteger("🗑️ Delete Member", "Enter Member ID to delete:",
                                           parent=self.root)
        if member_id and messagebox.askyesno("Confirm Delete",
                                            f"Are you sure you want to delete member ID {member_id}?\n\nThis action cannot be undone.",
                                            parent=self.root):
            try:
                self.library.delete_member(member_id)
                self.refresh_data()
                self.update_status(f"Member ID {member_id} deleted successfully", "success")
            except Exception as e:
                messagebox.showerror("Error", f"Delete failed: {str(e)}")
                self.update_status("Failed to delete member", "error")

    def borrow_book_dialog(self):
        """Show modern borrow book dialog"""
        dialog = ModernBorrowDialog(self.root, self.library, "Borrow Book")
        if dialog.result:
            self.refresh_data()
            self.update_status(f"Book borrowed successfully - Transaction ID: {dialog.result}", "success")

    def return_book_dialog(self):
        """Show modern return book dialog"""
        dialog = ModernReturnDialog(self.root, self.library, "Return Book")
        if dialog.result:
            self.refresh_data()
            self.update_status("Book returned successfully", "success")

    def view_overdue_books(self):
        """Show overdue books in modern format"""
        try:
            overdue_books = self.library.get_overdue_books()
            if overdue_books:
                self.show_modern_search_results("Overdue Books", overdue_books, "overdue items")
            else:
                messagebox.showinfo("📚 Overdue Books", "Great news! No overdue books found.",
                                   parent=self.root)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to get overdue books: {str(e)}")

    def show_modern_search_results(self, title, results, search_term):
        """Show search results in modern window"""
        if not results:
            messagebox.showinfo("🔍 Search Results",
                               f"No {title.lower()} found for '{search_term}'",
                               parent=self.root)
            return

        # Create modern results window
        result_window = tk.Toplevel(self.root)
        result_window.title(f"🔍 {title} - Search Results")
        result_window.geometry("900x500")
        result_window.configure(bg=self.colors['light'])
        result_window.transient(self.root)
        result_window.grab_set()

        # Header
        header_frame = tk.Frame(result_window, bg=self.colors['primary'], height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        header_label = tk.Label(header_frame,
                               text=f"🔍 Found {len(results)} {title} for '{search_term}'",
                               font=('Segoe UI', 16, 'bold'),
                               fg=self.colors['white'], bg=self.colors['primary'])
        header_label.pack(expand=True)

        # Results area
        results_frame = tk.Frame(result_window, bg=self.colors['white'])
        results_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Create scrollable text area
        text_widget = tk.Text(results_frame, wrap=tk.WORD, font=('Segoe UI', 11),
                             bg=self.colors['white'], fg=self.colors['dark'])
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        # Format and insert results
        for i, item in enumerate(results, 1):
            text_widget.insert(tk.END, f"📋 Result {i}:\n", 'header')
            for key, value in item.items():
                text_widget.insert(tk.END, f"   {key.replace('_', ' ').title()}: {value}\n")
            text_widget.insert(tk.END, "\n" + "─" * 50 + "\n\n")

        # Configure text tags
        text_widget.tag_configure('header', font=('Segoe UI', 12, 'bold'),
                                 foreground=self.colors['primary'])

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Close button
        close_btn = tk.Button(result_window, text="✖ Close",
                             font=('Segoe UI', 10), fg=self.colors['white'],
                             bg=self.colors['danger'], relief=tk.FLAT,
                             command=result_window.destroy, cursor='hand2',
                             padx=20, pady=8)
        close_btn.pack(pady=10)

    def create_books_table(self):
        """Create books table"""
        columns = ('ID', 'Title', 'Author', 'ISBN', 'Year', 'Category', 'Available')
        self.books_tree = ttk.Treeview(self.books_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.books_tree.heading(col, text=col)
            self.books_tree.column(col, width=100)
        
        books_scrollbar = ttk.Scrollbar(self.books_frame, orient=tk.VERTICAL, command=self.books_tree.yview)
        self.books_tree.configure(yscrollcommand=books_scrollbar.set)
        
        self.books_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        books_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_members_table(self):
        """Create members table"""
        columns = ('ID', 'Name', 'Email', 'Phone', 'Status', 'Joined Date')
        self.members_tree = ttk.Treeview(self.members_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.members_tree.heading(col, text=col)
            self.members_tree.column(col, width=120)
        
        members_scrollbar = ttk.Scrollbar(self.members_frame, orient=tk.VERTICAL, command=self.members_tree.yview)
        self.members_tree.configure(yscrollcommand=members_scrollbar.set)
        
        self.members_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        members_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_transactions_table(self):
        """Create transactions table"""
        columns = ('ID', 'Book Title', 'Member Name', 'Type', 'Date', 'Due Date', 'Status')
        self.transactions_tree = ttk.Treeview(self.transactions_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.transactions_tree.heading(col, text=col)
            self.transactions_tree.column(col, width=120)
        
        transactions_scrollbar = ttk.Scrollbar(self.transactions_frame, orient=tk.VERTICAL, command=self.transactions_tree.yview)
        self.transactions_tree.configure(yscrollcommand=transactions_scrollbar.set)
        
        self.transactions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        transactions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_statistics_panel(self):
        """Create statistics panel"""
        stats_label_frame = ttk.LabelFrame(self.stats_frame, text="Library Statistics", padding="10")
        stats_label_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.stats_labels = {}
        stats_items = [
            ('Total Books', 'total_books'),
            ('Available Books', 'available_books'),
            ('Borrowed Books', 'borrowed_books'),
            ('Total Members', 'total_members'),
            ('Active Members', 'active_members'),
            ('Overdue Books', 'overdue_books')
        ]
        
        for i, (label_text, key) in enumerate(stats_items):
            row = i // 2
            col = i % 2
            
            frame = ttk.Frame(stats_label_frame)
            frame.grid(row=row, column=col, padx=20, pady=10, sticky=tk.W)
            
            ttk.Label(frame, text=f"{label_text}:", font=(GUI_CONFIG['font_family'], 12, 'bold')).pack(anchor=tk.W)
            self.stats_labels[key] = ttk.Label(frame, text="0", font=(GUI_CONFIG['font_family'], 14))
            self.stats_labels[key].pack(anchor=tk.W)
    
    def create_status_bar(self, parent):
        """Create status bar"""
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(parent, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def update_status(self, message):
        """Update status bar message"""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def refresh_data(self):
        """Refresh all data in the GUI"""
        self.update_status("Refreshing data...")
        try:
            self.load_books()
            self.load_members()
            self.load_transactions()
            self.load_statistics()
            self.update_status("Data refreshed successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh data: {str(e)}")
            self.update_status("Error refreshing data")
    
    def load_books(self):
        """Load books into the table"""
        for item in self.books_tree.get_children():
            self.books_tree.delete(item)
        
        books = self.library.get_all_books()
        for book in books:
            self.books_tree.insert('', tk.END, values=(
                book['book_id'], book['title'], book['author'], book['isbn'],
                book['published_year'] or '', book['category'],
                'Yes' if book['available'] else 'No'
            ))
    
    def load_members(self):
        """Load members into the table"""
        for item in self.members_tree.get_children():
            self.members_tree.delete(item)
        
        members = self.library.get_all_members()
        for member in members:
            self.members_tree.insert('', tk.END, values=(
                member['member_id'], member['name'], member['email'],
                member['phone'] or '', member['status'].title(), member['joined_date']
            ))
    
    def load_transactions(self):
        """Load transactions into the table"""
        for item in self.transactions_tree.get_children():
            self.transactions_tree.delete(item)
        
        transactions = self.library.get_transaction_history(50)
        for transaction in transactions:
            self.transactions_tree.insert('', tk.END, values=(
                transaction['transaction_id'], transaction['title'], transaction['member_name'],
                transaction['transaction_type'].title(), transaction['transaction_date'],
                transaction['due_date'] or '', transaction['status'].title()
            ))
    
    def load_statistics(self):
        """Load statistics"""
        stats = self.library.get_library_stats()
        for key, label in self.stats_labels.items():
            label.config(text=str(stats.get(key, 0)))

    # Dialog methods
    def add_book_dialog(self):
        """Show add book dialog"""
        try:
            title = simpledialog.askstring("Add Book", "Enter book title:")
            if not title: return

            author = simpledialog.askstring("Add Book", "Enter author:")
            if not author: return

            isbn = simpledialog.askstring("Add Book", "Enter ISBN:")
            if not isbn: return

            year_str = simpledialog.askstring("Add Book", "Enter published year (optional):")
            year = int(year_str) if year_str and year_str.isdigit() else None

            category = simpledialog.askstring("Add Book", "Enter category (optional):") or "General"

            book_id = self.library.add_book(title, author, isbn, year, category)
            messagebox.showinfo("Success", f"Book added successfully! ID: {book_id}")
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add book: {str(e)}")

    def add_member_dialog(self):
        """Show add member dialog"""
        try:
            name = simpledialog.askstring("Add Member", "Enter member name:")
            if not name: return

            email = simpledialog.askstring("Add Member", "Enter email:")
            if not email: return

            phone = simpledialog.askstring("Add Member", "Enter phone (optional):") or ""
            address = simpledialog.askstring("Add Member", "Enter address (optional):") or ""

            member_id = self.library.add_member(name, email, phone, address)
            messagebox.showinfo("Success", f"Member added successfully! ID: {member_id}")
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add member: {str(e)}")

    def search_books_dialog(self):
        """Show search books dialog"""
        search_term = simpledialog.askstring("Search Books", "Enter search term:")
        if search_term:
            try:
                books = self.library.search_books(search_term)
                self.show_search_results("Books", books)
            except Exception as e:
                messagebox.showerror("Error", f"Search failed: {str(e)}")

    def search_members_dialog(self):
        """Show search members dialog"""
        search_term = simpledialog.askstring("Search Members", "Enter search term:")
        if search_term:
            try:
                members = self.library.search_members(search_term)
                self.show_search_results("Members", members)
            except Exception as e:
                messagebox.showerror("Error", f"Search failed: {str(e)}")

    def delete_book_dialog(self):
        """Show delete book dialog"""
        book_id = simpledialog.askinteger("Delete Book", "Enter Book ID:")
        if book_id and messagebox.askyesno("Confirm", f"Delete book ID {book_id}?"):
            try:
                self.library.delete_book(book_id)
                messagebox.showinfo("Success", "Book deleted successfully!")
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("Error", f"Delete failed: {str(e)}")

    def delete_member_dialog(self):
        """Show delete member dialog"""
        member_id = simpledialog.askinteger("Delete Member", "Enter Member ID:")
        if member_id and messagebox.askyesno("Confirm", f"Delete member ID {member_id}?"):
            try:
                self.library.delete_member(member_id)
                messagebox.showinfo("Success", "Member deleted successfully!")
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("Error", f"Delete failed: {str(e)}")

    def borrow_book_dialog(self):
        """Show borrow book dialog"""
        try:
            book_id = simpledialog.askinteger("Borrow Book", "Enter Book ID:")
            if not book_id: return

            member_id = simpledialog.askinteger("Borrow Book", "Enter Member ID:")
            if not member_id: return

            transaction_id = self.library.borrow_book(book_id, member_id)
            messagebox.showinfo("Success", f"Book borrowed! Transaction ID: {transaction_id}")
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("Error", f"Borrow failed: {str(e)}")

    def return_book_dialog(self):
        """Show return book dialog"""
        try:
            book_id = simpledialog.askinteger("Return Book", "Enter Book ID:")
            if not book_id: return

            member_id = simpledialog.askinteger("Return Book", "Enter Member ID:")
            if not member_id: return

            self.library.return_book(book_id, member_id)
            messagebox.showinfo("Success", "Book returned successfully!")
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("Error", f"Return failed: {str(e)}")

    def view_overdue_books(self):
        """Show overdue books"""
        try:
            overdue_books = self.library.get_overdue_books()
            if overdue_books:
                self.show_search_results("Overdue Books", overdue_books)
            else:
                messagebox.showinfo("Overdue Books", "No overdue books found!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to get overdue books: {str(e)}")

    def show_search_results(self, title, results):
        """Show search results in a new window"""
        if not results:
            messagebox.showinfo("Search Results", "No results found!")
            return

        result_window = tk.Toplevel(self.root)
        result_window.title(f"{title} - Search Results")
        result_window.geometry("800x400")

        # Create text widget to show results
        text_widget = tk.Text(result_window, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(result_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        # Format and insert results
        for i, item in enumerate(results, 1):
            text_widget.insert(tk.END, f"{i}. ")
            for key, value in item.items():
                text_widget.insert(tk.END, f"{key}: {value}, ")
            text_widget.insert(tk.END, "\n\n")

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)


# Alias for backward compatibility
LibraryGUI = ModernLibraryGUI
