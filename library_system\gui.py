import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from .library import Library
from config import GUI_CONFIG

class LibraryGUI:
    def __init__(self, root):
        self.root = root
        self.library = Library()
        self.setup_window()
        self.create_widgets()
        self.refresh_data()
    
    def setup_window(self):
        """Setup main window properties"""
        self.root.title(GUI_CONFIG['window_title'])
        self.root.geometry(GUI_CONFIG['window_size'])
        self.root.configure(bg=GUI_CONFIG['bg_color'])
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_width() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_height() // 2)
        self.root.geometry(f"+{x}+{y}")
    
    def create_widgets(self):
        """Create and arrange GUI widgets"""
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Library Management System", 
                               font=(GUI_CONFIG['font_family'], 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Left panel - Controls
        self.create_control_panel(main_frame)
        
        # Right panel - Data display
        self.create_data_panel(main_frame)
        
        # Status bar
        self.create_status_bar(main_frame)
    
    def create_control_panel(self, parent):
        """Create control panel with buttons"""
        control_frame = ttk.LabelFrame(parent, text="Controls", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # Book management
        book_frame = ttk.LabelFrame(control_frame, text="Book Management", padding="5")
        book_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(book_frame, text="Add Book", command=self.add_book_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(book_frame, text="Search Books", command=self.search_books_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(book_frame, text="Delete Book", command=self.delete_book_dialog).pack(fill=tk.X, pady=2)
        
        # Member management
        member_frame = ttk.LabelFrame(control_frame, text="Member Management", padding="5")
        member_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(member_frame, text="Add Member", command=self.add_member_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(member_frame, text="Search Members", command=self.search_members_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(member_frame, text="Delete Member", command=self.delete_member_dialog).pack(fill=tk.X, pady=2)
        
        # Transactions
        transaction_frame = ttk.LabelFrame(control_frame, text="Transactions", padding="5")
        transaction_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(transaction_frame, text="Borrow Book", command=self.borrow_book_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(transaction_frame, text="Return Book", command=self.return_book_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(transaction_frame, text="View Overdue", command=self.view_overdue_books).pack(fill=tk.X, pady=2)
        
        # Refresh button
        ttk.Button(control_frame, text="Refresh All", command=self.refresh_data).pack(fill=tk.X, pady=(10, 0))
    
    def create_data_panel(self, parent):
        """Create data display panel with tabs"""
        self.notebook = ttk.Notebook(parent)
        self.notebook.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Books tab
        self.books_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.books_frame, text="Books")
        self.create_books_table()
        
        # Members tab
        self.members_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.members_frame, text="Members")
        self.create_members_table()
        
        # Transactions tab
        self.transactions_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.transactions_frame, text="Transactions")
        self.create_transactions_table()
        
        # Statistics tab
        self.stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.stats_frame, text="Statistics")
        self.create_statistics_panel()
    
    def create_books_table(self):
        """Create books table"""
        columns = ('ID', 'Title', 'Author', 'ISBN', 'Year', 'Category', 'Available')
        self.books_tree = ttk.Treeview(self.books_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.books_tree.heading(col, text=col)
            self.books_tree.column(col, width=100)
        
        books_scrollbar = ttk.Scrollbar(self.books_frame, orient=tk.VERTICAL, command=self.books_tree.yview)
        self.books_tree.configure(yscrollcommand=books_scrollbar.set)
        
        self.books_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        books_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_members_table(self):
        """Create members table"""
        columns = ('ID', 'Name', 'Email', 'Phone', 'Status', 'Joined Date')
        self.members_tree = ttk.Treeview(self.members_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.members_tree.heading(col, text=col)
            self.members_tree.column(col, width=120)
        
        members_scrollbar = ttk.Scrollbar(self.members_frame, orient=tk.VERTICAL, command=self.members_tree.yview)
        self.members_tree.configure(yscrollcommand=members_scrollbar.set)
        
        self.members_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        members_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_transactions_table(self):
        """Create transactions table"""
        columns = ('ID', 'Book Title', 'Member Name', 'Type', 'Date', 'Due Date', 'Status')
        self.transactions_tree = ttk.Treeview(self.transactions_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.transactions_tree.heading(col, text=col)
            self.transactions_tree.column(col, width=120)
        
        transactions_scrollbar = ttk.Scrollbar(self.transactions_frame, orient=tk.VERTICAL, command=self.transactions_tree.yview)
        self.transactions_tree.configure(yscrollcommand=transactions_scrollbar.set)
        
        self.transactions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        transactions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_statistics_panel(self):
        """Create statistics panel"""
        stats_label_frame = ttk.LabelFrame(self.stats_frame, text="Library Statistics", padding="10")
        stats_label_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.stats_labels = {}
        stats_items = [
            ('Total Books', 'total_books'),
            ('Available Books', 'available_books'),
            ('Borrowed Books', 'borrowed_books'),
            ('Total Members', 'total_members'),
            ('Active Members', 'active_members'),
            ('Overdue Books', 'overdue_books')
        ]
        
        for i, (label_text, key) in enumerate(stats_items):
            row = i // 2
            col = i % 2
            
            frame = ttk.Frame(stats_label_frame)
            frame.grid(row=row, column=col, padx=20, pady=10, sticky=tk.W)
            
            ttk.Label(frame, text=f"{label_text}:", font=(GUI_CONFIG['font_family'], 12, 'bold')).pack(anchor=tk.W)
            self.stats_labels[key] = ttk.Label(frame, text="0", font=(GUI_CONFIG['font_family'], 14))
            self.stats_labels[key].pack(anchor=tk.W)
    
    def create_status_bar(self, parent):
        """Create status bar"""
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(parent, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def update_status(self, message):
        """Update status bar message"""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def refresh_data(self):
        """Refresh all data in the GUI"""
        self.update_status("Refreshing data...")
        try:
            self.load_books()
            self.load_members()
            self.load_transactions()
            self.load_statistics()
            self.update_status("Data refreshed successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh data: {str(e)}")
            self.update_status("Error refreshing data")
    
    def load_books(self):
        """Load books into the table"""
        for item in self.books_tree.get_children():
            self.books_tree.delete(item)
        
        books = self.library.get_all_books()
        for book in books:
            self.books_tree.insert('', tk.END, values=(
                book['book_id'], book['title'], book['author'], book['isbn'],
                book['published_year'] or '', book['category'],
                'Yes' if book['available'] else 'No'
            ))
    
    def load_members(self):
        """Load members into the table"""
        for item in self.members_tree.get_children():
            self.members_tree.delete(item)
        
        members = self.library.get_all_members()
        for member in members:
            self.members_tree.insert('', tk.END, values=(
                member['member_id'], member['name'], member['email'],
                member['phone'] or '', member['status'].title(), member['joined_date']
            ))
    
    def load_transactions(self):
        """Load transactions into the table"""
        for item in self.transactions_tree.get_children():
            self.transactions_tree.delete(item)
        
        transactions = self.library.get_transaction_history(50)
        for transaction in transactions:
            self.transactions_tree.insert('', tk.END, values=(
                transaction['transaction_id'], transaction['title'], transaction['member_name'],
                transaction['transaction_type'].title(), transaction['transaction_date'],
                transaction['due_date'] or '', transaction['status'].title()
            ))
    
    def load_statistics(self):
        """Load statistics"""
        stats = self.library.get_library_stats()
        for key, label in self.stats_labels.items():
            label.config(text=str(stats.get(key, 0)))

    # Dialog methods
    def add_book_dialog(self):
        """Show add book dialog"""
        try:
            title = simpledialog.askstring("Add Book", "Enter book title:")
            if not title: return

            author = simpledialog.askstring("Add Book", "Enter author:")
            if not author: return

            isbn = simpledialog.askstring("Add Book", "Enter ISBN:")
            if not isbn: return

            year_str = simpledialog.askstring("Add Book", "Enter published year (optional):")
            year = int(year_str) if year_str and year_str.isdigit() else None

            category = simpledialog.askstring("Add Book", "Enter category (optional):") or "General"

            book_id = self.library.add_book(title, author, isbn, year, category)
            messagebox.showinfo("Success", f"Book added successfully! ID: {book_id}")
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add book: {str(e)}")

    def add_member_dialog(self):
        """Show add member dialog"""
        try:
            name = simpledialog.askstring("Add Member", "Enter member name:")
            if not name: return

            email = simpledialog.askstring("Add Member", "Enter email:")
            if not email: return

            phone = simpledialog.askstring("Add Member", "Enter phone (optional):") or ""
            address = simpledialog.askstring("Add Member", "Enter address (optional):") or ""

            member_id = self.library.add_member(name, email, phone, address)
            messagebox.showinfo("Success", f"Member added successfully! ID: {member_id}")
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add member: {str(e)}")

    def search_books_dialog(self):
        """Show search books dialog"""
        search_term = simpledialog.askstring("Search Books", "Enter search term:")
        if search_term:
            try:
                books = self.library.search_books(search_term)
                self.show_search_results("Books", books)
            except Exception as e:
                messagebox.showerror("Error", f"Search failed: {str(e)}")

    def search_members_dialog(self):
        """Show search members dialog"""
        search_term = simpledialog.askstring("Search Members", "Enter search term:")
        if search_term:
            try:
                members = self.library.search_members(search_term)
                self.show_search_results("Members", members)
            except Exception as e:
                messagebox.showerror("Error", f"Search failed: {str(e)}")

    def delete_book_dialog(self):
        """Show delete book dialog"""
        book_id = simpledialog.askinteger("Delete Book", "Enter Book ID:")
        if book_id and messagebox.askyesno("Confirm", f"Delete book ID {book_id}?"):
            try:
                self.library.delete_book(book_id)
                messagebox.showinfo("Success", "Book deleted successfully!")
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("Error", f"Delete failed: {str(e)}")

    def delete_member_dialog(self):
        """Show delete member dialog"""
        member_id = simpledialog.askinteger("Delete Member", "Enter Member ID:")
        if member_id and messagebox.askyesno("Confirm", f"Delete member ID {member_id}?"):
            try:
                self.library.delete_member(member_id)
                messagebox.showinfo("Success", "Member deleted successfully!")
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("Error", f"Delete failed: {str(e)}")

    def borrow_book_dialog(self):
        """Show borrow book dialog"""
        try:
            book_id = simpledialog.askinteger("Borrow Book", "Enter Book ID:")
            if not book_id: return

            member_id = simpledialog.askinteger("Borrow Book", "Enter Member ID:")
            if not member_id: return

            transaction_id = self.library.borrow_book(book_id, member_id)
            messagebox.showinfo("Success", f"Book borrowed! Transaction ID: {transaction_id}")
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("Error", f"Borrow failed: {str(e)}")

    def return_book_dialog(self):
        """Show return book dialog"""
        try:
            book_id = simpledialog.askinteger("Return Book", "Enter Book ID:")
            if not book_id: return

            member_id = simpledialog.askinteger("Return Book", "Enter Member ID:")
            if not member_id: return

            self.library.return_book(book_id, member_id)
            messagebox.showinfo("Success", "Book returned successfully!")
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("Error", f"Return failed: {str(e)}")

    def view_overdue_books(self):
        """Show overdue books"""
        try:
            overdue_books = self.library.get_overdue_books()
            if overdue_books:
                self.show_search_results("Overdue Books", overdue_books)
            else:
                messagebox.showinfo("Overdue Books", "No overdue books found!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to get overdue books: {str(e)}")

    def show_search_results(self, title, results):
        """Show search results in a new window"""
        if not results:
            messagebox.showinfo("Search Results", "No results found!")
            return

        result_window = tk.Toplevel(self.root)
        result_window.title(f"{title} - Search Results")
        result_window.geometry("800x400")

        # Create text widget to show results
        text_widget = tk.Text(result_window, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(result_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        # Format and insert results
        for i, item in enumerate(results, 1):
            text_widget.insert(tk.END, f"{i}. ")
            for key, value in item.items():
                text_widget.insert(tk.END, f"{key}: {value}, ")
            text_widget.insert(tk.END, "\n\n")

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
