# Library Management System

A comprehensive library management system with a graphical user interface and MySQL database integration.

## Features

- **Book Management**: Add, search, and delete books
- **Member Management**: Add, search, and manage library members
- **Transaction Management**: Borrow and return books with due date tracking
- **Overdue Tracking**: Monitor and view overdue books
- **Statistics Dashboard**: View library statistics and metrics
- **User-Friendly GUI**: Intuitive graphical interface built with <PERSON>kinter
- **MySQL Integration**: Robust database backend with proper relationships

## Requirements

- Python 3.7 or higher
- MySQL Server 5.7 or higher
- Required Python packages (see requirements.txt)

## Installation

1. **Clone or download the project**
   ```bash
   cd "DBMS Project"
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure MySQL connection**
   - Edit `config.py` file
   - Update the database configuration with your MySQL credentials:
   ```python
   DATABASE_CONFIG = {
       'host': 'localhost',
       'user': 'your_username',
       'password': 'your_password',
       'database': 'library_management',
       'port': 3306,
       'charset': 'utf8mb4',
       'autocommit': True
   }
   ```

4. **Setup the database**
   ```bash
   python setup_database.py
   ```

5. **Run the application**
   ```bash
   python main.py
   ```

## Database Schema

The system uses three main tables:

### Books Table
- `book_id` (Primary Key)
- `title`
- `author`
- `isbn` (Unique)
- `published_year`
- `category`
- `available` (Boolean)
- `created_at`, `updated_at` (Timestamps)

### Members Table
- `member_id` (Primary Key)
- `name`
- `email` (Unique)
- `phone`
- `address`
- `joined_date`
- `status` (active/inactive)
- `created_at`, `updated_at` (Timestamps)

### Transactions Table
- `transaction_id` (Primary Key)
- `book_id` (Foreign Key)
- `member_id` (Foreign Key)
- `transaction_type` (borrow/return)
- `transaction_date`
- `due_date`
- `return_date`
- `fine_amount`
- `status` (active/completed/overdue)
- `created_at` (Timestamp)

## Usage

### Main Interface
The application opens with a tabbed interface showing:
- **Books**: View all books in the library
- **Members**: View all registered members
- **Transactions**: View borrowing history
- **Statistics**: View library statistics

### Adding Books
1. Click "Add Book" button
2. Fill in the required fields (Title, Author, ISBN)
3. Optionally add Published Year and Category
4. Click "Add Book" to save

### Adding Members
1. Click "Add Member" button
2. Fill in the required fields (Name, Email)
3. Optionally add Phone and Address
4. Click "Add Member" to save

### Borrowing Books
1. Click "Borrow Book" button
2. Enter Book ID and Member ID
3. Optionally set due days (default: 14)
4. Click "Borrow" to process

### Returning Books
1. Click "Return Book" button
2. Enter Book ID and Member ID
3. Click "Return" to process

### Search Functionality
- Use "Search Books" to find books by title, author, or ISBN
- Use "Search Members" to find members by name or email
- Results are displayed in a separate window

### Viewing Overdue Books
- Click "View Overdue" to see all books that are past their due date
- Shows book details, member information, and due dates

## File Structure

```
DBMS Project/
├── library_system/
│   ├── __init__.py
│   ├── database.py      # MySQL database operations
│   ├── models.py        # Data models and validation
│   ├── library.py       # Business logic
│   └── gui.py          # GUI application
├── config.py           # Configuration settings
├── database_setup.sql  # Database schema and sample data
├── setup_database.py   # Database setup script
├── main.py            # Application entry point
├── requirements.txt   # Python dependencies
└── README.md         # This file
```

## Troubleshooting

### Database Connection Issues
- Ensure MySQL server is running
- Check credentials in `config.py`
- Verify user has necessary privileges
- Test connection with: `python setup_database.py`

### Import Errors
- Ensure all dependencies are installed: `pip install -r requirements.txt`
- Check Python version (3.7+ required)

### GUI Issues
- Ensure tkinter is installed (usually comes with Python)
- On Linux, you might need: `sudo apt-get install python3-tk`

## Sample Data

The setup script includes sample data:
- 5 sample books
- 3 sample members
- You can add more data through the GUI

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is for educational purposes.
