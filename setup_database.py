#!/usr/bin/env python3
"""
Database setup script for Library Management System
This script creates the database and tables required for the application.
"""

import mysql.connector
from mysql.connector import <PERSON>rro<PERSON>
from config import DATABASE_CONFIG
import sys

def create_database():
    """Create the database if it doesn't exist"""
    try:
        # Connect without specifying database
        config = DATABASE_CONFIG.copy()
        database_name = config.pop('database')
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # Create database
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {database_name}")
        print(f"Database '{database_name}' created successfully!")
        
        cursor.close()
        connection.close()
        
    except Error as e:
        print(f"Error creating database: {e}")
        return False
    
    return True

def setup_tables():
    """Create tables and insert sample data"""
    try:
        # Read SQL file
        with open('database_setup.sql', 'r') as file:
            sql_script = file.read()
        
        # Connect to database
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor()
        
        # Execute SQL script
        # Split by semicolon and execute each statement
        statements = sql_script.split(';')
        for statement in statements:
            statement = statement.strip()
            if statement:
                cursor.execute(statement)
        
        connection.commit()
        print("Tables created and sample data inserted successfully!")
        
        cursor.close()
        connection.close()
        
    except Error as e:
        print(f"Error setting up tables: {e}")
        return False
    except FileNotFoundError:
        print("Error: database_setup.sql file not found!")
        return False
    
    return True

def test_connection():
    """Test database connection"""
    try:
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor()
        
        # Test query
        cursor.execute("SELECT COUNT(*) FROM books")
        book_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM members")
        member_count = cursor.fetchone()[0]
        
        print(f"Connection successful!")
        print(f"Books in database: {book_count}")
        print(f"Members in database: {member_count}")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Error as e:
        print(f"Connection test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("Library Management System - Database Setup")
    print("=" * 50)
    
    # Check if MySQL is accessible
    try:
        config = DATABASE_CONFIG.copy()
        config.pop('database')
        test_conn = mysql.connector.connect(**config)
        test_conn.close()
        print("✓ MySQL server is accessible")
    except Error as e:
        print(f"✗ Cannot connect to MySQL server: {e}")
        print("\nPlease ensure:")
        print("1. MySQL server is running")
        print("2. Connection details in config.py are correct")
        print("3. User has necessary privileges")
        sys.exit(1)
    
    # Create database
    print("\n1. Creating database...")
    if not create_database():
        sys.exit(1)
    
    # Setup tables
    print("\n2. Setting up tables...")
    if not setup_tables():
        sys.exit(1)
    
    # Test connection
    print("\n3. Testing connection...")
    if not test_connection():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("Database setup completed successfully!")
    print("You can now run the application with: python main.py")

if __name__ == "__main__":
    main()
