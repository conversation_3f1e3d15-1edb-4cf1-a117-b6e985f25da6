from .models import Book, Member
from .database import Database
from datetime import datetime

class Library:
    def __init__(self):
        self.db = Database()
    
    def add_book(self, title, author, isbn, published_year):
        book_id = len(self.db.books) + 1
        book = Book(book_id, title, author, isbn, published_year).__dict__
        self.db.books.append(book)
        self.db.save_data()
        return book_id
    
    def add_member(self, name, email):
        member_id = len(self.db.members) + 1
        member = Member(member_id, name, email, str(datetime.now().date())).__dict__
        self.db.members.append(member)
        self.db.save_data()
        return member_id
        
    def borrow_book(self, book_id, member_id):
        # Implementation details
        pass
        
    def return_book(self, book_id, member_id):
        # Implementation details
        pass