from .models import Book, Member, Transaction
from .database import Database
from datetime import datetime

class Library:
    def __init__(self):
        self.db = Database()
    
    def __del__(self):
        """Cleanup database connection"""
        if hasattr(self, 'db'):
            self.db.disconnect()
    
    # Book management methods
    def add_book(self, title, author, isbn, published_year, category="General"):
        """Add a new book to the library"""
        book = Book(title=title, author=author, isbn=isbn, 
                   published_year=published_year, category=category)
        
        # Validate book data
        errors = book.validate()
        if errors:
            raise ValueError("; ".join(errors))
        
        try:
            book_id = self.db.add_book(title, author, isbn, published_year, category)
            return book_id
        except Exception as e:
            if "Duplicate entry" in str(e):
                raise ValueError("A book with this ISBN already exists")
            raise
    
    def get_all_books(self):
        """Get all books in the library"""
        return self.db.get_all_books()
    
    def search_books(self, search_term):
        """Search for books by title, author, or ISBN"""
        return self.db.search_books(search_term)
    
    def delete_book(self, book_id):
        """Delete a book from the library"""
        # Check if book is currently borrowed
        borrowed_books = self.db.get_borrowed_books()
        for transaction in borrowed_books:
            if transaction['book_id'] == book_id:
                raise ValueError("Cannot delete book that is currently borrowed")
        
        return self.db.delete_book(book_id)
    
    # Member management methods
    def add_member(self, name, email, phone="", address=""):
        """Add a new member to the library"""
        member = Member(name=name, email=email, phone=phone, address=address)
        
        # Validate member data
        errors = member.validate()
        if errors:
            raise ValueError("; ".join(errors))
        
        try:
            member_id = self.db.add_member(name, email, phone, address)
            return member_id
        except Exception as e:
            if "Duplicate entry" in str(e):
                raise ValueError("A member with this email already exists")
            raise
    
    def get_all_members(self):
        """Get all members of the library"""
        return self.db.get_all_members()
    
    def search_members(self, search_term):
        """Search for members by name or email"""
        return self.db.search_members(search_term)
    
    def update_member_status(self, member_id, status):
        """Update member status (active/inactive)"""
        if status not in ['active', 'inactive']:
            raise ValueError("Status must be 'active' or 'inactive'")
        
        return self.db.update_member_status(member_id, status)
    
    def delete_member(self, member_id):
        """Delete a member from the library"""
        # Check if member has borrowed books
        borrowed_books = self.db.get_borrowed_books(member_id)
        if borrowed_books:
            raise ValueError("Cannot delete member who has borrowed books")
        
        return self.db.delete_member(member_id)
    
    # Transaction methods
    def borrow_book(self, book_id, member_id, due_days=14):
        """Borrow a book"""
        # Check if book exists and is available
        books = self.db.execute_query("SELECT * FROM books WHERE book_id = %s", (book_id,))
        if not books:
            raise ValueError("Book not found")
        
        book = books[0]
        if not book['available']:
            raise ValueError("Book is not available for borrowing")
        
        # Check if member exists and is active
        members = self.db.execute_query("SELECT * FROM members WHERE member_id = %s", (member_id,))
        if not members:
            raise ValueError("Member not found")
        
        member = members[0]
        if member['status'] != 'active':
            raise ValueError("Member account is not active")
        
        # Create borrowing transaction
        transaction_id = self.db.borrow_book(book_id, member_id, due_days)
        return transaction_id
    
    def return_book(self, book_id, member_id):
        """Return a borrowed book"""
        # Check if there's an active borrowing transaction
        borrowed_books = self.db.get_borrowed_books(member_id)
        active_transaction = None
        
        for transaction in borrowed_books:
            if transaction['book_id'] == book_id:
                active_transaction = transaction
                break
        
        if not active_transaction:
            raise ValueError("No active borrowing record found for this book and member")
        
        # Process return
        success = self.db.return_book(book_id, member_id)
        return success
    
    def get_borrowed_books(self, member_id=None):
        """Get all currently borrowed books"""
        return self.db.get_borrowed_books(member_id)
    
    def get_overdue_books(self):
        """Get all overdue books"""
        return self.db.get_overdue_books()
    
    def get_transaction_history(self, limit=100):
        """Get transaction history"""
        return self.db.get_transaction_history(limit)
    
    # Statistics methods
    def get_library_stats(self):
        """Get library statistics"""
        stats = {}
        
        # Total books
        all_books = self.get_all_books()
        stats['total_books'] = len(all_books)
        stats['available_books'] = len([b for b in all_books if b['available']])
        stats['borrowed_books'] = stats['total_books'] - stats['available_books']
        
        # Total members
        all_members = self.get_all_members()
        stats['total_members'] = len(all_members)
        stats['active_members'] = len([m for m in all_members if m['status'] == 'active'])
        
        # Overdue books
        overdue_books = self.get_overdue_books()
        stats['overdue_books'] = len(overdue_books)
        
        return stats
