#!/usr/bin/env python3
"""
Test MySQL connection
"""

try:
    from config import DATABASE_CONFIG
    import mysql.connector
    from mysql.connector import Error
    
    print("Testing MySQL connection...")
    print(f"Host: {DATABASE_CONFIG['host']}")
    print(f"User: {DATABASE_CONFIG['user']}")
    print(f"Port: {DATABASE_CONFIG['port']}")
    
    # Test connection without database
    config = DATABASE_CONFIG.copy()
    config.pop('database')  # Remove database name for initial connection
    
    connection = mysql.connector.connect(**config)
    print("✓ MySQL connection successful!")
    
    cursor = connection.cursor()
    cursor.execute("SELECT VERSION()")
    version = cursor.fetchone()
    print(f"✓ MySQL version: {version[0]}")
    
    cursor.close()
    connection.close()
    
    print("\n✅ MySQL is ready! You can now run the setup script.")
    
except Error as e:
    print(f"❌ MySQL connection failed: {e}")
    print("\n🔧 Troubleshooting steps:")
    print("1. Make sure MySQL server is running")
    print("2. Check if XAMPP MySQL service is started")
    print("3. Verify username and password in config.py")
    print("4. Try connecting with a MySQL client first")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure mysql-connector-python is installed")
    
except Exception as e:
    print(f"❌ Unexpected error: {e}")
