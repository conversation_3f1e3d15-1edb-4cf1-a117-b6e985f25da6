class Book:
    def __init__(self, book_id, title, author, isbn, published_year, available=True):
        self.book_id = book_id
        self.title = title
        self.author = author
        self.isbn = isbn
        self.published_year = published_year
        self.available = available
        
class Member:
    def __init__(self, member_id, name, email, joined_date):
        self.member_id = member_id
        self.name = name
        self.email = email
        self.joined_date = joined_date
        self.borrowed_books = []