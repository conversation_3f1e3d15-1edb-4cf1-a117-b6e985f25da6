from datetime import datetime
import re

class Book:
    def __init__(self, book_id=None, title="", author="", isbn="", published_year=None, 
                 category="General", available=True, created_at=None, updated_at=None):
        self.book_id = book_id
        self.title = title
        self.author = author
        self.isbn = isbn
        self.published_year = published_year
        self.category = category
        self.available = available
        self.created_at = created_at
        self.updated_at = updated_at
    
    def validate(self):
        """Validate book data"""
        errors = []
        
        if not self.title.strip():
            errors.append("Title is required")
        
        if not self.author.strip():
            errors.append("Author is required")
        
        if not self.isbn.strip():
            errors.append("ISBN is required")
        elif not self.is_valid_isbn(self.isbn):
            errors.append("Invalid ISBN format")
        
        if self.published_year:
            current_year = datetime.now().year
            if not (1000 <= self.published_year <= current_year):
                errors.append(f"Published year must be between 1000 and {current_year}")
        
        return errors
    
    @staticmethod
    def is_valid_isbn(isbn):
        """Validate ISBN format (ISBN-10 or ISBN-13)"""
        # Remove hyphens and spaces
        isbn = re.sub(r'[-\s]', '', isbn)
        
        # Check ISBN-10 or ISBN-13
        if len(isbn) == 10:
            return isbn.isdigit() or (isbn[:-1].isdigit() and isbn[-1].upper() == 'X')
        elif len(isbn) == 13:
            return isbn.isdigit()
        
        return False
    
    def to_dict(self):
        """Convert book object to dictionary"""
        return {
            'book_id': self.book_id,
            'title': self.title,
            'author': self.author,
            'isbn': self.isbn,
            'published_year': self.published_year,
            'category': self.category,
            'available': self.available,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

class Member:
    def __init__(self, member_id=None, name="", email="", phone="", address="", 
                 joined_date=None, status="active", created_at=None, updated_at=None):
        self.member_id = member_id
        self.name = name
        self.email = email
        self.phone = phone
        self.address = address
        self.joined_date = joined_date or datetime.now().date()
        self.status = status
        self.created_at = created_at
        self.updated_at = updated_at
    
    def validate(self):
        """Validate member data"""
        errors = []
        
        if not self.name.strip():
            errors.append("Name is required")
        
        if not self.email.strip():
            errors.append("Email is required")
        elif not self.is_valid_email(self.email):
            errors.append("Invalid email format")
        
        if self.phone and not self.is_valid_phone(self.phone):
            errors.append("Invalid phone number format")
        
        return errors
    
    @staticmethod
    def is_valid_email(email):
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def is_valid_phone(phone):
        """Validate phone number format"""
        # Remove spaces, hyphens, parentheses
        phone = re.sub(r'[\s\-\(\)]', '', phone)
        # Check if it's all digits and has reasonable length
        return phone.isdigit() and 10 <= len(phone) <= 15
    
    def to_dict(self):
        """Convert member object to dictionary"""
        return {
            'member_id': self.member_id,
            'name': self.name,
            'email': self.email,
            'phone': self.phone,
            'address': self.address,
            'joined_date': self.joined_date,
            'status': self.status,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

class Transaction:
    def __init__(self, transaction_id=None, book_id=None, member_id=None, 
                 transaction_type="borrow", transaction_date=None, due_date=None, 
                 return_date=None, fine_amount=0.0, status="active", created_at=None):
        self.transaction_id = transaction_id
        self.book_id = book_id
        self.member_id = member_id
        self.transaction_type = transaction_type
        self.transaction_date = transaction_date or datetime.now().date()
        self.due_date = due_date
        self.return_date = return_date
        self.fine_amount = fine_amount
        self.status = status
        self.created_at = created_at
    
    def calculate_fine(self, daily_fine_rate=1.0):
        """Calculate fine for overdue books"""
        if self.due_date and not self.return_date:
            today = datetime.now().date()
            if today > self.due_date:
                overdue_days = (today - self.due_date).days
                return overdue_days * daily_fine_rate
        return 0.0
    
    def to_dict(self):
        """Convert transaction object to dictionary"""
        return {
            'transaction_id': self.transaction_id,
            'book_id': self.book_id,
            'member_id': self.member_id,
            'transaction_type': self.transaction_type,
            'transaction_date': self.transaction_date,
            'due_date': self.due_date,
            'return_date': self.return_date,
            'fine_amount': self.fine_amount,
            'status': self.status,
            'created_at': self.created_at
        }
