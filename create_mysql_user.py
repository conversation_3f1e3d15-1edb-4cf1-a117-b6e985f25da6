#!/usr/bin/env python3
"""
Create a MySQL user for the library system
"""

import mysql.connector
from mysql.connector import Error
import getpass

def create_user():
    print("MySQL User Creation for Library Management System")
    print("=" * 50)
    
    # Get root credentials
    print("\nFirst, we need your MySQL root credentials:")
    root_password = getpass.getpass("Enter MySQL root password: ")
    
    try:
        # Connect as root
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password=root_password,
            port=3306
        )
        
        cursor = connection.cursor()
        print("✓ Connected to MySQL as root")
        
        # Create new user
        new_username = 'library_user'
        new_password = 'library123'
        
        print(f"\nCreating user: {new_username}")
        print(f"Password: {new_password}")
        
        # Drop user if exists
        try:
            cursor.execute(f"DROP USER IF EXISTS '{new_username}'@'localhost'")
        except:
            pass
        
        # Create user
        cursor.execute(f"CREATE USER '{new_username}'@'localhost' IDENTIFIED BY '{new_password}'")
        
        # <PERSON> privileges
        cursor.execute(f"GRANT ALL PRIVILEGES ON *.* TO '{new_username}'@'localhost'")
        cursor.execute("FLUSH PRIVILEGES")
        
        print("✓ User created successfully!")
        
        # Update config file
        print("\nUpdating config.py...")
        
        config_content = f'''# Database configuration
DATABASE_CONFIG = {{
    'host': 'localhost',
    'user': '{new_username}',
    'password': '{new_password}',
    'database': 'library_management',
    'port': 3306,
    'charset': 'utf8mb4',
    'autocommit': True
}}

# GUI Configuration
GUI_CONFIG = {{
    'window_title': 'Library Management System',
    'window_size': '1200x800',
    'theme_color': '#2E86AB',
    'bg_color': '#F8F9FA',
    'font_family': 'Arial',
    'font_size': 10
}}
'''
        
        with open('config.py', 'w') as f:
            f.write(config_content)
        
        print("✓ config.py updated!")
        
        cursor.close()
        connection.close()
        
        print("\n" + "=" * 50)
        print("✅ Setup complete!")
        print(f"New MySQL user: {new_username}")
        print(f"Password: {new_password}")
        print("\nYou can now run: python setup_database.py")
        
    except Error as e:
        print(f"❌ Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure you entered the correct root password")
        print("2. Make sure MySQL server is running")
        print("3. Try connecting with MySQL Workbench or command line first")

if __name__ == "__main__":
    create_user()
