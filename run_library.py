#!/usr/bin/env python3
"""
Library Management System Launcher
This script properly sets up the Python path and launches the application.
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """Main application entry point"""
    try:
        # Import after setting up the path
        from library_system.gui import LibraryGUI
        
        print("Starting Library Management System...")
        
        # Create main window
        root = tk.Tk()
        
        # Create and run the GUI application
        app = LibraryGUI(root)
        
        # Handle window close event
        def on_closing():
            if messagebox.askokcancel("Quit", "Do you want to quit the Library Management System?"):
                try:
                    app.library.db.disconnect()
                    print("Database connection closed.")
                except:
                    pass
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        print("Library Management System started successfully!")
        print("Close this terminal window to stop the application.")
        
        # Start the GUI event loop
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Make sure all files are in the correct location:")
        print("- library_system/")
        print("  - __init__.py")
        print("  - gui.py")
        print("  - library.py")
        print("  - database.py")
        print("  - models.py")
        input("Press Enter to exit...")
        
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        messagebox.showerror("Error", f"Failed to start application: {str(e)}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
