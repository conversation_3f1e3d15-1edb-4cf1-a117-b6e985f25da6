import mysql.connector
from mysql.connector import <PERSON>rror
from config import DATABASE_CONFIG
from datetime import datetime, timedelta

class Database:
    def __init__(self):
        self.connection = None
        self.cursor = None
        self.connect()
    
    def connect(self):
        """Establish connection to MySQL database"""
        try:
            self.connection = mysql.connector.connect(**DATABASE_CONFIG)
            self.cursor = self.connection.cursor(dictionary=True)
            print("Successfully connected to MySQL database")
        except Error as e:
            print(f"Error connecting to MySQL: {e}")
            raise
    
    def disconnect(self):
        """Close database connection"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
    
    def execute_query(self, query, params=None):
        """Execute a query and return results"""
        try:
            self.cursor.execute(query, params or ())
            if query.strip().upper().startswith('SELECT'):
                return self.cursor.fetchall()
            else:
                self.connection.commit()
                return self.cursor.rowcount
        except Error as e:
            print(f"Database error: {e}")
            self.connection.rollback()
            raise
    
    # Book operations
    def add_book(self, title, author, isbn, published_year, category="General"):
        """Add a new book to the database"""
        query = """
        INSERT INTO books (title, author, isbn, published_year, category)
        VALUES (%s, %s, %s, %s, %s)
        """
        params = (title, author, isbn, published_year, category)
        self.execute_query(query, params)
        return self.cursor.lastrowid
    
    def get_all_books(self):
        """Get all books from the database"""
        query = "SELECT * FROM books ORDER BY title"
        return self.execute_query(query)
    
    def search_books(self, search_term):
        """Search books by title, author, or ISBN"""
        query = """
        SELECT * FROM books 
        WHERE title LIKE %s OR author LIKE %s OR isbn LIKE %s
        ORDER BY title
        """
        search_pattern = f"%{search_term}%"
        params = (search_pattern, search_pattern, search_pattern)
        return self.execute_query(query, params)
    
    def update_book_availability(self, book_id, available):
        """Update book availability status"""
        query = "UPDATE books SET available = %s WHERE book_id = %s"
        params = (available, book_id)
        return self.execute_query(query, params)
    
    def delete_book(self, book_id):
        """Delete a book from the database"""
        query = "DELETE FROM books WHERE book_id = %s"
        params = (book_id,)
        return self.execute_query(query, params)
    
    # Member operations
    def add_member(self, name, email, phone="", address=""):
        """Add a new member to the database"""
        query = """
        INSERT INTO members (name, email, phone, address)
        VALUES (%s, %s, %s, %s)
        """
        params = (name, email, phone, address)
        self.execute_query(query, params)
        return self.cursor.lastrowid
    
    def get_all_members(self):
        """Get all members from the database"""
        query = "SELECT * FROM members ORDER BY name"
        return self.execute_query(query)
    
    def search_members(self, search_term):
        """Search members by name or email"""
        query = """
        SELECT * FROM members 
        WHERE name LIKE %s OR email LIKE %s
        ORDER BY name
        """
        search_pattern = f"%{search_term}%"
        params = (search_pattern, search_pattern)
        return self.execute_query(query, params)
    
    def update_member_status(self, member_id, status):
        """Update member status"""
        query = "UPDATE members SET status = %s WHERE member_id = %s"
        params = (status, member_id)
        return self.execute_query(query, params)
    
    def delete_member(self, member_id):
        """Delete a member from the database"""
        query = "DELETE FROM members WHERE member_id = %s"
        params = (member_id,)
        return self.execute_query(query, params)
    
    # Transaction operations
    def borrow_book(self, book_id, member_id, due_days=14):
        """Record a book borrowing transaction"""
        due_date = datetime.now().date() + timedelta(days=due_days)
        
        # Insert transaction record
        query = """
        INSERT INTO transactions (book_id, member_id, transaction_type, due_date)
        VALUES (%s, %s, 'borrow', %s)
        """
        params = (book_id, member_id, due_date)
        self.execute_query(query, params)
        
        # Update book availability
        self.update_book_availability(book_id, False)
        
        return self.cursor.lastrowid
    
    def return_book(self, book_id, member_id):
        """Record a book return transaction"""
        return_date = datetime.now().date()
        
        # Update the active transaction
        query = """
        UPDATE transactions 
        SET return_date = %s, status = 'completed'
        WHERE book_id = %s AND member_id = %s AND status = 'active'
        """
        params = (return_date, book_id, member_id)
        self.execute_query(query, params)
        
        # Update book availability
        self.update_book_availability(book_id, True)
        
        return True
    
    def get_borrowed_books(self, member_id=None):
        """Get all currently borrowed books"""
        if member_id:
            query = """
            SELECT t.*, b.title, b.author, m.name as member_name
            FROM transactions t
            JOIN books b ON t.book_id = b.book_id
            JOIN members m ON t.member_id = m.member_id
            WHERE t.member_id = %s AND t.status = 'active'
            ORDER BY t.due_date
            """
            params = (member_id,)
        else:
            query = """
            SELECT t.*, b.title, b.author, m.name as member_name
            FROM transactions t
            JOIN books b ON t.book_id = b.book_id
            JOIN members m ON t.member_id = m.member_id
            WHERE t.status = 'active'
            ORDER BY t.due_date
            """
            params = None
        
        return self.execute_query(query, params)
    
    def get_overdue_books(self):
        """Get all overdue books"""
        query = """
        SELECT t.*, b.title, b.author, m.name as member_name, m.email
        FROM transactions t
        JOIN books b ON t.book_id = b.book_id
        JOIN members m ON t.member_id = m.member_id
        WHERE t.status = 'active' AND t.due_date < CURDATE()
        ORDER BY t.due_date
        """
        return self.execute_query(query)
    
    def get_transaction_history(self, limit=100):
        """Get transaction history"""
        query = """
        SELECT t.*, b.title, b.author, m.name as member_name
        FROM transactions t
        JOIN books b ON t.book_id = b.book_id
        JOIN members m ON t.member_id = m.member_id
        ORDER BY t.created_at DESC
        LIMIT %s
        """
        params = (limit,)
        return self.execute_query(query, params)
