import json
import os

class Database:
    def __init__(self, db_file="library_db.json"):
        self.db_file = db_file
        self.books = []
        self.members = []
        self.load_data()
        
    def load_data(self):
        if os.path.exists(self.db_file):
            with open(self.db_file, 'r') as f:
                data = json.load(f)
                self.books = data.get('books', [])
                self.members = data.get('members', [])
    
    def save_data(self):
        with open(self.db_file, 'w') as f:
            json.dump({
                'books': self.books,
                'members': self.members
            }, f, indent=2)